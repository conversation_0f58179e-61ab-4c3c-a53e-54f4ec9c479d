# app/services/nlp_service.py
import json
import logging
import os
import pickle
import random
import re
import traceback
import uuid
import warnings

import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from flask import current_app, request
from sklearn.feature_extraction.text import CountVectorizer
from wordcloud import WordCloud

from app.config import Config

matplotlib.use("Agg")  # GUI가 필요없는 백엔드 사용
import base64
import json
import logging
import os
import random
import uuid
from collections import defaultdict
from io import BytesIO
from math import pi

import gensim
import gensim.corpora as corpora
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
import pandas as pd
# (1-4) LDA 시각화 및 데이터 변환 라이브러리
import pyLDAvis
import pyLDAvis.gensim
import spacy
from gensim.models.coherencemodel import CoherenceModel
from gensim.models.ldamodel import LdaModel
from konlpy.tag import Hannanum, Kkma, Komoran, Okt
from matplotlib import font_manager
from sklearn.feature_extraction.text import CountVectorizer, TfidfTransformer
from wordcloud import WordCloud

from bertopic import BERTopic
from sentence_transformers import SentenceTransformer
from sklearn.feature_extraction.text import CountVectorizer
import io
import base64
import matplotlib.pyplot as plt
import plotly
import json
import traceback
import os

from ..services.session_service import get_or_create_session_id

logger = logging.getLogger(__name__)


try:
    from adjustText import adjust_text

    has_adjust_text = True
except ImportError:
    has_adjust_text = False

#
try:
    font_path = None
    # 운영체제별 기본 한글 폰트 경로 확인
    if os.name == "nt":  # 윈도우
        font_path = current_app.config["FONT_PATH"]
    elif os.name == "posix":  # macOS 또는 Linux
        if os.path.exists("/Library/Fonts/AppleGothic.ttf"):  # macOS
            font_path = "/Library/Fonts/AppleGothic.ttf"
        else:  # Linux
            # 리눅스에서 일반적인 한글 폰트 경로들
            possible_paths = [
                "/usr/share/fonts/truetype/nanum/NanumGothic.ttf",
                "/usr/share/fonts/nanum/NanumGothic.ttf",
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    font_path = path
                    break

    # (2-2) 폰트 설정 적용
    if font_path:
        font_name = font_manager.FontProperties(fname=font_path).get_name()
        plt.rc("font", family=font_name)
    else:
        # 폰트 경로를 찾지 못한 경우, 시스템 기본 폰트 사용
        plt.rc("font", family="sans-serif")
except Exception as e:
    print(f"한글 폰트 설정 오류: {e}")
    # 오류 발생 시 기본 폰트 사용
    plt.rc("font", family="sans-serif")

# (2-3) 그래프 음수 기호 표시 문제 해결
matplotlib.rcParams["axes.unicode_minus"] = False


# (3-2) 전역 변수 설정 (모델 및 데이터 저장용)
global_model = None
global_corpus = None
global_dictionary = None
global_tokens = None
global_edited_keywords = {}

# (3-3) LdaModel 관리 함수


# Helper function to get session ID
def get_current_session_id():
    """Get the current session ID from the request"""
    session_id = request.args.get("session_id")
    if not session_id:
        # Get or create a session and return the session ID
        session_id = get_or_create_session_id()

        # Ensure session directories exist
        from app.services.json_session_service import \
            ensure_session_directories

        ensure_session_directories(session_id)
    return session_id


def get_lda_model(session_id=None):
    """
    전역 LdaModel 인스턴스를 가져오거나, 없는 경우 세션에서 복원합니다.

    Returns:
        tuple: (model, corpus, dictionary) - 현재 LdaModel 인스턴스와 관련 데이터 또는 (None, None, None)
    """

    # 이미 로드된 모델이 있으면 반환

    # Try to load model from session
    if session_id is None:
        session_id = get_current_session_id()

    model, corpus, dictionary = load_lda_model(session_id)
    if model is not None:
        global_model = model
        global_corpus = corpus
        global_dictionary = dictionary
        return model, corpus, dictionary

    return None, None, None


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Ignore spaCy warnings
warnings.filterwarnings("ignore", message=r".*\[E0*")

# Try to load spaCy model
try:
    nlp_en = spacy.load("en_core_web_sm")
    SPACY_AVAILABLE = True
except:
    SPACY_AVAILABLE = False

# 형태소 분석기 분류
LANGUAGE_GROUPS = {
    "korean": {
        "name": "한국어",
        "analyzers": {
            "okt": {"name": "Open Korean Text (Okt)", "instance": Okt()},
            "hannanum": {"name": "한나눔 (Hannanum)", "instance": Hannanum()},
            "kkma": {"name": "꼬꼬마 (Kkma)", "instance": Kkma()},
            "komoran": {"name": "코모란 (Komoran)", "instance": Komoran()},
        },
    },
    "english": {
        "name": "영어",
        "analyzers": {
            "spacy": {"name": "spaCy", "instance": nlp_en if SPACY_AVAILABLE else None}
        },
    },
}

# 품사 태깅 설정 (영어 기본값 변경)
DEFAULT_TAGS = {
    "korean": ["Noun"],  # 한국어 기본값: 명사만 추출
    "english": ["Noun"],  # 영어 기본값: 명사만 추출 (통합)
}

# Korean common POS tag mapping
KOREAN_TAG_MAPPING = {
    "okt": {
        "Noun": ["Noun"],
        "Verb": ["Verb"],
        "Adjective": ["Adjective"],
        "Adverb": ["Adverb"],
        "Determiner": ["Determiner"],
        "Exclamation": ["Exclamation"],
        "Josa": ["Josa"],
        "Eomi": ["Eomi"],
        "Suffix": ["Suffix"],
        "Prefix": ["PreEomi", "Prefix"],
    },
    "hannanum": {
        "Noun": ["N"],
        "Verb": ["P", "XP", "VP"],
        "Adjective": ["PA"],
        "Adverb": ["MAG"],
        "Determiner": ["MD"],
        "Exclamation": ["II"],
        "Josa": ["J"],
        "Eomi": ["E"],
        "Suffix": ["X"],
        "Prefix": [],
    },
    "kkma": {
        "Noun": ["NNG", "NNP", "NNB", "NNM", "NP"],
        "Verb": ["VV", "VXV", "VX"],
        "Adjective": ["VA", "VXA", "VCP", "VCN"],
        "Adverb": ["MAG", "MAJ"],
        "Determiner": ["MDT", "MDN"],
        "Exclamation": ["IC"],
        "Josa": ["JKS", "JKC", "JKG", "JKO", "JKM", "JKI", "JKQ", "JX", "JC"],
        "Eomi": [
            "EPH",
            "EPT",
            "EPP",
            "EFN",
            "EFQ",
            "EFO",
            "EFA",
            "EFI",
            "EFR",
            "ECE",
            "ECS",
            "ECD",
            "ETN",
            "ETD",
        ],
        "Suffix": ["XSN", "XSV", "XSA"],
        "Prefix": ["XPN", "XPV"],
    },
    "komoran": {
        "Noun": ["NNG", "NNP", "NNB"],
        "Verb": ["VV"],
        "Adjective": ["VA"],
        "Adverb": ["MAG"],
        "Determiner": ["MM"],
        "Exclamation": ["IC"],
        "Josa": ["JKS", "JKC", "JKG", "JKO", "JKB", "JKV", "JKQ", "JX", "JC"],
        "Eomi": ["EP", "EF", "EC", "ETN", "ETM"],
        "Suffix": ["XSN", "XSV", "XSA"],
        "Prefix": ["XPN"],
    },
}

# 영어 품사 태그 (변경 없음 - 실제 spaCy 태그는 유지)
ENGLISH_POS_TAGS = {
    "NN": "명사(단수)",
    "NNS": "명사(복수)",
    "NNP": "고유명사(단수)",
    "NNPS": "고유명사(복수)",
    "JJ": "형용사",
    "JJR": "형용사(비교급)",
    "JJS": "형용사(최상급)",
    "VB": "동사(원형)",
    "VBD": "동사(과거)",
    "VBG": "동사(현재분사)",
    "VBN": "동사(과거분사)",
    "VBP": "동사(현재형)",
    "VBZ": "동사(3인칭 단수)",
    "RB": "부사",
    "RBR": "부사(비교급)",
    "RBS": "부사(최상급)",
}

FONT_PATH = Config.FONT_PATH
MAX_FEATURES = 300  # 최대 분석 단어 수


def get_language_groups():
    """Return available language groups and analyzers"""
    groups = {}
    for lang_key, lang_value in LANGUAGE_GROUPS.items():
        analyzers = {}
        for analyzer_key, analyzer_value in lang_value["analyzers"].items():
            # Disable English analyzer if spaCy is not installed
            if (
                lang_key == "english"
                and analyzer_key == "spacy"
                and not SPACY_AVAILABLE
            ):
                continue
            analyzers[analyzer_key] = analyzer_value["name"]

        groups[lang_key] = {"name": lang_value["name"], "analyzers": analyzers}

    return groups


def get_pos_tags(language, analyzer):
    """선택한 언어와 분석기에 따른 품사 태그 목록 반환 (영어 단순화)"""
    if language == "korean":
        # 한국어는 명사, 동사, 형용사만 제공
        pos_tags = {
            "Noun": "명사",
            "Verb": "동사",
            "Adjective": "형용사",
            # 'Adverb': '부사',
            # 'Determiner': '관형사',
            # 'Exclamation': '감탄사',
            # 'Josa': '조사',
            # 'Eomi': '어미',
            # 'Suffix': '접미사',
            # 'Prefix': '접두사'
        }
    elif language == "english":
        # 영어는 통합된 명사, 형용사, 동사만 제공
        pos_tags = {
            "Noun": "명사",  # 통합된 명사 태그
            "JJ": "형용사",  # 형용사
            "VB": "동사(원형)",  # 동사
        }
    else:
        pos_tags = {}

    return pos_tags


def get_spacy_status():
    """Check if spaCy model is installed"""
    return {"installed": SPACY_AVAILABLE}


def tokenize_korean(text, analyzer_name="okt", selected_tags=None, min_word_length=2):
    """한국어 텍스트를 토큰화하고 선택된 품사만 추출"""
    # 기본값 설정
    if selected_tags is None or len(selected_tags) == 0:
        selected_tags = DEFAULT_TAGS["korean"]

    # 텍스트 전처리 (None, NaN 처리 및 빈 문자열 체크)
    if text is None or pd.isna(text) or text.strip() == "":
        return []

    # 텍스트 타입 변환 (항상 문자열로 처리)
    text = str(text).strip()

    # 분석기 가져오기
    if analyzer_name not in LANGUAGE_GROUPS["korean"]["analyzers"]:
        analyzer_name = "okt"  # 기본값

    analyzer = LANGUAGE_GROUPS["korean"]["analyzers"][analyzer_name]["instance"]

    # 선택된 품사에 해당하는 실제 태그 가져오기
    actual_tags = []
    for tag in selected_tags:
        if tag in KOREAN_TAG_MAPPING[analyzer_name]:
            actual_tags.extend(KOREAN_TAG_MAPPING[analyzer_name][tag])

    # 빈 태그 리스트 체크
    if not actual_tags:
        return []

    # 형태소 분석 진행 (각 분석기별 예외 처리 추가)
    try:
        if analyzer_name == "okt":
            # Okt는 기본적으로 pos() 메서드 사용
            morphs = analyzer.pos(text)
        elif analyzer_name == "hannanum":
            # 한나눔은 analyze() 후 pos() 사용 (타임아웃 설정)
            # 한나눔의 pos() 함수는 문자열을 반환하는 경우가 있어 analyze() 후 태깅
            morphs = []
            analyzed = analyzer.analyze(text)
            for ana in analyzed:
                pos_tagged = analyzer.tag_pos(ana)
                morphs.extend(pos_tagged)
        elif analyzer_name == "kkma":
            # 꼬꼬마는 텍스트가 긴 경우 처리 문제가 있어 문장 단위로 나누어 처리
            sentences = text.split(".")  # 문장 단위로 분리
            morphs = []
            for sentence in sentences:
                if sentence.strip():  # 빈 문장은 건너뛰기
                    try:
                        # 문장별로 처리
                        sentence_morphs = analyzer.pos(sentence)
                        morphs.extend(sentence_morphs)
                    except:
                        # 개별 문장 처리 실패 시 무시하고 계속 진행
                        continue
        elif analyzer_name == "komoran":
            # 코모란의 경우 기본 .pos() 함수 사용
            morphs = analyzer.pos(text)
        else:
            morphs = []
    except Exception as e:
        print(f"형태소 분석 오류 ({analyzer_name}): {str(e)}")
        return []

    # 선택된 품사만 필터링
    words = []
    for word, tag in morphs:
        if tag in actual_tags:
            if len(word) >= min_word_length:
                words.append(word)

    return words


def tokenize_english(text, selected_tags=None, min_word_length=2):
    """영어 텍스트를 토큰화하고 선택된 품사만 추출 (통합된 Noun 처리)"""
    if not SPACY_AVAILABLE:
        return ["[spaCy 모델이 설치되지 않음]"]

    # 기본값 설정
    if selected_tags is None or len(selected_tags) == 0:
        selected_tags = DEFAULT_TAGS["english"]

    # 실제 spaCy 태그로 변환
    actual_spacy_tags = set()
    if "Noun" in selected_tags:
        actual_spacy_tags.update(["NN", "NNS", "NNP", "NNPS"])
    if "JJ" in selected_tags:
        actual_spacy_tags.add("JJ")  # 형용사 추가
        # 필요하다면 비교급, 최상급도 추가 가능: actual_spacy_tags.update(['JJ', 'JJR', 'JJS'])
    if "VB" in selected_tags:
        actual_spacy_tags.add("VB")  # 동사 원형 추가
        # 필요하다면 다른 동사 형태도 추가 가능: actual_spacy_tags.update(['VB', 'VBD', 'VBG', 'VBN', 'VBP', 'VBZ'])

    # 선택된 태그가 없으면 빈 리스트 반환
    if not actual_spacy_tags:
        return []

    doc = nlp_en(text)
    words = []
    for token in doc:
        if token.tag_ in actual_spacy_tags:  # 변환된 실제 태그 사용
            if len(token.lemma_) >= min_word_length:
                # 소문자 변환 및 불용어 제외
                lemma = token.lemma_.lower()
                if not token.is_stop and lemma.isalpha():
                    words.append(lemma)

    return words


def remove_special_characters_and_numbers(text):
    """
    영어 알파벳(소문자 및 대문자), 밑줄(_), 한국어, 일본어 (히라가나, 가타카나), 중국어 문자를 제외한 모든 문자와 숫자를 제거
    """
    # 영어 알파벳 및 밑줄, 공백, 한국어, 일본어, 중국어 문자를 제외한 모든 것을 제거하는 패턴
    pattern = (
        r"[^\sa-zA-Z_\uAC00-\uD7A3\u3040-\u309F\u30A0-\u30FF\u31F0-\u31FF\u4E00-\u9FFF]"
    )
    # 정규 표현식을 사용하여 패턴에 일치하지 않는 모든 문자를 제거
    result = re.sub(pattern, "", text)
    return result


def read_file(file_path):
    """파일을 읽어서 데이터프레임으로 반환하는 함수"""
    try:
        # 파일 확장자 확인
        if file_path.lower().endswith((".xlsx", ".xls")):
            # 엑셀 파일인 경우
            data = pd.read_excel(file_path)
        else:
            # CSV 파일 시도 (다양한 인코딩 방식으로)
            try:
                # UTF-8 인코딩으로 시도
                data = pd.read_csv(file_path, encoding="utf-8")
            except:
                try:
                    # CP949 인코딩으로 시도
                    data = pd.read_csv(file_path, encoding="cp949")
                except:
                    try:
                        # 파이썬 엔진으로 구분자 자동 감지 시도
                        data = pd.read_csv(
                            file_path, encoding="utf-8", engine="python", sep=None
                        )
                    except:
                        # 마지막으로 엑셀 파일로 시도
                        data = pd.read_excel(file_path)

        return data
    except Exception as e:
        # logger.error(f"파일 읽기 오류: {e}")
        raise Exception(
            "파일을 읽을 수 없습니다. CSV 또는 Excel 파일 형식이 맞는지 확인해주세요."
        )


# def create_wordcloud_mask(shape, width=1000, height=1000):
#     """워드클라우드 마스크 생성"""
#     if shape == "circle":
#         x, y = np.ogrid[:width, :height]
#         center = width / 2, height / 2
#         radius = min(width, height) / 2 * 0.9  # 90% of the minimum dimension

#         # 원 바깥 영역을 선택 (True)
#         circle_mask = (x - center[0]) ** 2 + (y - center[1]) ** 2 > radius**2

#         # 마스크 반전: 워드클라우드는 값이 0인 영역에 단어를 그립니다
#         # 따라서 원 안쪽을 0으로, 바깥쪽을 255로 설정
#         return 255 * circle_mask.astype(int)
#     return None


# def get_color_function(color_theme):
#     """워드클라우드 색상 함수 생성"""
#     colormap = plt.cm.get_cmap(color_theme)

#     def color_func(*args, **kwargs):
#         return tuple(int(x * 255) for x in colormap(random.random()))

#     return color_func


def get_file_path(session_id, filename):
    """세션 ID와 파일명을 기반으로 파일 경로 생성"""
    return os.path.join(current_app.config["UPLOAD_FOLDER"], session_id, filename)


def analyze_frequency(
    file_path,
    column_name,
    selection_type="top_n",
    max_words=50,
    cloud_shape="rectangle",
    cloud_color="viridis",
    selected_words=None,
):
    """단어 빈도 분석 및 워드 클라우드 생성 함수"""
    try:
        # 파일 읽기
        data = read_file(file_path)

        # 지정한 컬럼이 있는지 확인
        if column_name not in data.columns:
            return {"error": f"'{column_name}' 컬럼을 찾을 수 없습니다."}

        # 결과 파일용 고유 파일명 생성
        filename = f"frequency_{uuid.uuid4().hex[:8]}"

        # 비문자열 값 처리 (NaN을 빈 문자열로 변환하고 모든 값을 문자열로 변환)
        data[column_name] = data[column_name].fillna("").astype(str)

        # 단어 빈도 분석
        cv = CountVectorizer(max_features=MAX_FEATURES, ngram_range=(1, 1))
        tdm = cv.fit_transform(data[column_name])

        # 단어와 빈도를 데이터프레임으로 변환
        word_count_df = pd.DataFrame(
            {"단어": cv.get_feature_names_out(), "빈도": tdm.sum(axis=0).tolist()[0]}
        )

        # 빈도수 기준으로 내림차순 정렬
        word_count_df = word_count_df.sort_values("빈도", ascending=False)
        word_count_df = word_count_df.reset_index(drop=True)

        # 전체 단어 빈도수 합계 계산
        total_word_count = word_count_df["빈도"].sum()

        # 각 단어의 빈도 비율(%) 계산
        word_count_df["비율(%)"] = (word_count_df["빈도"] / total_word_count) * 100

        # 세션별 폴더 구조 설정
        session_id = get_current_session_id()
        result_folder = current_app.config["RESULT_FOLDER"]
        session_folder = os.path.join(result_folder, session_id)
        word_freq_folder = os.path.join(
            session_folder, current_app.config["WORD_FREQ_FOLDER"]
        )

        # 필요한 디렉토리 생성
        try:
            os.makedirs(word_freq_folder, exist_ok=True)
        except Exception as dir_error:
            logger.error(f"단어 빈도 폴더 생성 오류: {dir_error}")

        # 결과를 CSV 파일로 저장
        csv_filename = f"{filename}_frequency.csv"
        csv_path = os.path.join(word_freq_folder, csv_filename)
        word_count_df.to_csv(csv_path, encoding="cp949", index=False)

        # 상위 200개 단어 목록 생성 (항상 단어 데이터 제공)
        top_df = word_count_df.head(200).rename(
            columns={"단어": "word", "빈도": "frequency", "비율(%)": "ratio"}
        )
        top_df["ratio"] = ((top_df["ratio"] / 100).round(3)).astype("object")
        top_words = top_df.to_dict("records")
        # Convert frequency to float type
        # for item in top_words:
        #     item['frequency'] = float(item['frequency'])

        # 수동 선택 모드인 경우 단어 목록만 반환
        if selection_type == "manual" and selected_words is None:
            return {
                "success": True,
                "output_file": csv_filename,
                "word_data": top_words,
            }

        # 워드 클라우드 생성
        wordcloud_filename = f"{filename}_wordcloud.png"
        word_cloud_folder = os.path.join(
            session_folder, current_app.config["WORD_CLOUD_FOLDER"]
        )

        # 필요한 디렉토리 생성
        try:
            os.makedirs(word_cloud_folder, exist_ok=True)
        except Exception as dir_error:
            logger.error(f"워드클라우드 폴더 생성 오류: {dir_error}")

        wordcloud_path = os.path.join(word_cloud_folder, wordcloud_filename)

        # 워드 클라우드용 단어-빈도 사전 생성
        if selection_type == "manual" and selected_words:
            # 수동 선택 모드: 선택된 단어만 포함
            word_list = json.loads(selected_words)

            # 선택된 단어의 실제 빈도수 가져오기
            count_dict = {}
            for word in word_list:
                word_row = word_count_df[word_count_df["단어"] == word]
                if not word_row.empty:
                    count_dict[word] = word_row["빈도"].values[0]
                else:
                    count_dict[word] = 1  # 기본값
        else:
            # 자동 선택 모드: 상위 N개 단어 사용
            top_df = word_count_df.head(int(max_words))
            count_dict = dict(zip(top_df["단어"], top_df["빈도"]))

        # try:
        # 워드 클라우드 생성 (재사용)
        wordcloud_generator = WordCloudGenerator(analysis_type="word_frequency")
        wordcloud_result = wordcloud_generator.generate_wordcloud(
                count_dict, filename, cloud_shape, cloud_color
            )
        
        # generate_from_words(
        #     word_list, session_id, cloud_shape, cloud_color
        # )

        if wordcloud_result.get("success"):
            wordcloud_file = wordcloud_result["wordcloud_file"]
            has_wordcloud = True
        else:
            logger.error(f"워드클라우드 생성 오류: {wordcloud_result.get('error')}")
            wordcloud_file = None
        # except Exception as e:
        #     logger.error(f"워드클라우드 생성 오류: {e}")
        #     wordcloud_file = None

        # 결과 반환 (항상 단어 데이터 포함)
        # 상대 경로 반환 (세션 ID 포함)
        relative_csv_path = "/".join([
            current_app.config["WORD_FREQ_FOLDER"], csv_filename
        ])
        relative_wordcloud_path = (
            "/".join([current_app.config["WORD_CLOUD_FOLDER"], wordcloud_file])
            if has_wordcloud
            else None
        )

        return {
            "success": True,
            "output_file": relative_csv_path,
            "wordcloud_file": relative_wordcloud_path,
            "word_data": top_words,
        }

    except Exception as e:
        logger.error(f"단어 빈도 분석 오류: {traceback.format_exc()}")
        return {"error": str(e)}


# def create_wordcloud_from_words(word_list, cloud_shape, cloud_color):
#     """단어 목록으로 워드클라우드 생성"""
#     try:
#         if not word_list or len(word_list) < 10:
#             return {"error": "최소 10개 이상의 단어를 선택해주세요."}

#         # 결과 파일용 고유 파일명 생성
#         filename = f"wordcloud_{uuid.uuid4().hex[:8]}"
#         wordcloud_filename = f"{filename}.png"

#         # 세션별 폴더 구조 설정
#         session_id = get_current_session_id()
#         result_folder = current_app.config["RESULT_FOLDER"]
#         session_folder = os.path.join(result_folder, session_id)
#         word_cloud_folder = os.path.join(
#             session_folder, current_app.config["WORD_CLOUD_FOLDER"]
#         )

#         # 필요한 디렉토리 생성
#         try:
#             os.makedirs(word_cloud_folder, exist_ok=True)
#         except Exception as dir_error:
#             logger.error(f"워드클라우드 폴더 생성 오류: {dir_error}")

#         # 워드클라우드 파일 경로
#         wordcloud_path = os.path.join(word_cloud_folder, wordcloud_filename)

#         # 마스크와 색상 함수 생성
#         mask = create_wordcloud_mask(cloud_shape)
#         color_func = get_color_function(cloud_color)

#         # 단어-빈도 사전 생성 (모든 단어에 동일한 빈도 할당)
#         count_dict = {word: 1 for word in word_list}

#         # 워드 클라우드 생성 파라미터 (폰트 경로가 None이면 기본 폰트 사용)
#         wordcloud_params = {
#             "background_color": "white",
#             "max_words": len(count_dict),
#             "width": 1000,
#             "height": 1000,
#             "mask": mask,
#             "color_func": color_func,
#         }

#         # 폰트 경로가 있는 경우만 파라미터에 추가
#         if FONT_PATH:
#             wordcloud_params["font_path"] = FONT_PATH

#         # 워드 클라우드 생성
#         try:
#             wc = WordCloud(**wordcloud_params)
#             cloud = wc.fit_words(count_dict)
#             cloud.to_file(wordcloud_path)

#             # 파일이 실제로 생성되었는지 확인
#             if (
#                 not os.path.exists(wordcloud_path)
#                 or os.path.getsize(wordcloud_path) == 0
#             ):
#                 return {"error": "워드클라우드 생성에 실패했습니다. 리소스 접근 오류."}

#             # 상대 경로 반환 (세션 ID 포함)
#             relative_path = os.path.join(
#                 current_app.config["WORD_CLOUD_FOLDER"], wordcloud_filename
#             )

#             return {"success": True, "wordcloud_file": relative_path}
#         except Exception as inner_error:
#             logger.error(f"워드클라우드 생성 중 내부 오류: {inner_error}")
#             return {"error": f"워드클라우드 생성 오류: {str(inner_error)}"}
#     except Exception as e:
#         logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
#         return {"error": f"워드클라우드 처리 오류: {str(e)}"}


# ============================= Tokenization and Analysis =============================


# (5-1) pyLDAvis 시각화 업데이트 함수
def create_updated_pyldavis(model, corpus, dictionary, edited_keywords):
    try:
        # Check if required inputs are valid
        if model is None or corpus is None or dictionary is None:
            return "<div class='alert alert-warning'>모델, 말뭉치 또는 사전이 없습니다.</div>"

        # Check if corpus is empty
        if len(corpus) == 0:
            return "<div class='alert alert-warning'>말뭉치가 비어 있습니다.</div>"

        # Check if dictionary is empty
        if len(dictionary) == 0:
            return "<div class='alert alert-warning'>사전이 비어 있습니다.</div>"

        # (5-1-1) 편집된 키워드가 없을 경우 기본 시각화
        if not edited_keywords or all(
            not edits.get("removed") and not edits.get("edited")
            for edits in edited_keywords.values()
        ):
            try:
                vis_data = pyLDAvis.gensim.prepare(
                    model, corpus, dictionary, sort_topics=False
                )
                if vis_data is None:
                    return "<div class='alert alert-warning'>시각화 데이터를 생성할 수 없습니다.</div>"
                return pyLDAvis.prepared_data_to_html(vis_data)
            except Exception as e:
                logger.error(f"기본 pyLDAvis 생성 오류: {e}")
                return f"<div class='alert alert-danger'>기본 pyLDAvis 시각화 생성 실패: {str(e)}</div>"

        # (5-1-2) pyLDAvis 데이터 준비
        try:
            vis_data = pyLDAvis.gensim.prepare(
                model, corpus, dictionary, sort_topics=False
            )
            if vis_data is None:
                return "<div class='alert alert-warning'>시각화 데이터를 생성할 수 없습니다.</div>"
        except Exception as e:
            logger.error(f"pyLDAvis 데이터 준비 오류: {e}")
            return f"<div class='alert alert-danger'>pyLDAvis 데이터 준비 실패: {str(e)}</div>"

        # (5-1-3) 편집된 키워드를 pyLDAvis 데이터에 반영
        try:
            # (5-1-3-1) 제거된 단어 처리
            for topic_id, edits in edited_keywords.items():
                if "removed" in edits and edits["removed"]:
                    for removed_word in edits["removed"]:
                        # 제거할 단어가 있는 행 찾기
                        mask = (
                            vis_data.topic_info["Category"]
                            == "Topic" + str(topic_id + 1)
                        ) & (
                            vis_data.topic_info["Term"].str.lower()
                            == removed_word.lower()
                        )
                        # 가중치를 0으로 설정하여 시각화에서 제외
                        if mask.any():
                            vis_data.topic_info.loc[mask, "logprob"] = -9999
                            vis_data.topic_info.loc[mask, "loglift"] = -9999
                            vis_data.topic_info.loc[mask, "Freq"] = 0

                # (5-1-3-2) 편집된 단어 처리
                if "edited" in edits and edits["edited"]:
                    for original_word, new_word in edits["edited"].items():
                        # 편집할 단어가 있는 행 찾기
                        mask = (
                            vis_data.topic_info["Category"]
                            == "Topic" + str(topic_id + 1)
                        ) & (
                            vis_data.topic_info["Term"].str.lower()
                            == original_word.lower()
                        )
                        # 단어 이름 변경
                        if mask.any():
                            vis_data.topic_info.loc[mask, "Term"] = new_word

            # (5-1-4) 변경된 데이터로 HTML 생성
            return pyLDAvis.prepared_data_to_html(vis_data)

        except Exception as e:
            logger.error(f"pyLDAvis 데이터 편집 오류: {e}")
            # (5-1-5) 안전 모드로 기본 시각화 사용
            try:
                vis_data = pyLDAvis.gensim.prepare(
                    model, corpus, dictionary, sort_topics=False
                )
                if vis_data is None:
                    return "<div class='alert alert-warning'>시각화 데이터를 생성할 수 없습니다.</div>"
                return pyLDAvis.prepared_data_to_html(vis_data)
            except Exception as e2:
                logger.error(f"안전 모드 pyLDAvis 생성 오류: {e2}")
                return f"<div class='alert alert-danger'>pyLDAvis 시각화 생성 실패: {str(e2)}</div>"

    except Exception as e:
        logger.error(f"pyLDAvis 전체 처리 오류: {e}")
        # (5-1-6) 모든 경우의 오류에서 기본 시각화 생성 시도
        try:
            vis_data = pyLDAvis.gensim.prepare(
                model, corpus, dictionary, sort_topics=False
            )
            if vis_data is None:
                return "<div class='alert alert-warning'>시각화 데이터를 생성할 수 없습니다.</div>"
            return pyLDAvis.prepared_data_to_html(vis_data)
        except Exception as e2:
            logger.error(f"최종 pyLDAvis 생성 오류: {e2}")
            return f"<div class='alert alert-danger'>pyLDAvis 시각화 생성 실패: {str(e2)}</div>"


# (5-2) 토픽 단어 업데이트 함수
def get_updated_topics(model, edited_keywords):
    topics_formatted = []
    num_topics = model.num_topics

    # (5-2-1) 각 토픽별로 단어 업데이트
    for topic_id in range(num_topics):
        # (5-2-1-1) 편집 정보 가져오기
        topic_edits = edited_keywords.get(topic_id, {"edited": {}, "removed": []})
        edited_dict = topic_edits.get("edited", {})
        removed_list = topic_edits.get("removed", [])

        # (5-2-1-2) 원본 토픽 단어 가져오기
        topic_words = model.show_topic(topic_id, topn=15)  # (단어, 가중치) 튜플 리스트

        # (5-2-1-3) 단어 편집 및 제거 적용
        updated_words = []
        for word, weight in topic_words:
            # 제거 목록에 있는 단어 건너뛰기
            if word.lower() in removed_list:
                continue

            # 편집된 단어로 대체
            if word.lower() in edited_dict:
                updated_words.append((edited_dict[word.lower()], weight))
            else:
                updated_words.append((word, weight))

        # (5-2-1-4) 가중치로 정렬
        updated_words.sort(key=lambda x: x[1], reverse=True)

        # (5-2-1-5) 토픽 문자열 생성 - 원래 형식과 동일하게 유지
        # 기존 LDAModel.print_topics() 형식과 일치시키기: 원래는 "0.123*'word1' + 0.456*'word2' + ..."
        # 소수점 4자리로 반올림하되, gensim 기본 형식 유지
        topic_str = " + ".join(
            [f'{weight:.4f}*"{word}"' for word, weight in updated_words]
        )

        topics_formatted.append({"id": int(topic_id), "words": topic_str})

    return topics_formatted


# (5-3) 토픽 단어 가져오기 함수 (편집 반영)
def get_topic_words_with_edits(model, topic_id, edited_keywords):
    # (5-3-1) 편집 정보 가져오기
    topic_edits = edited_keywords.get(topic_id, {"edited": {}, "removed": []})
    edited_dict = topic_edits.get("edited", {})
    removed_list = topic_edits.get("removed", [])

    # (5-3-2) 원본 토픽 단어 가져오기
    topic_words = model.show_topic(topic_id, topn=30)  # 충분한 단어 가져오기

    # (5-3-3) 단어 편집 및 제거 적용
    updated_words = []
    for word, weight in topic_words:
        # 제거 목록에 있는 단어 건너뛰기
        if word.lower() in removed_list:
            continue

        # 편집된 단어로 대체
        if word.lower() in edited_dict:
            updated_words.append((edited_dict[word.lower()], weight))
        else:
            updated_words.append((word.lower(), weight))

    return updated_words


# (5-4) 토픽별 시각화 이미지 업데이트 함수
def update_topic_visualizations(model, edited_keywords, chart_style="default"):
    # 각 토픽별 시각화를 PNG 파일로 저장
    topic_images = []

    # 저장 디렉토리 확인 및 생성
    session_id = get_current_session_id()
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    topic_folder = os.path.join(
        session_folder, current_app.config["TOPIC_MODELS_FOLDER"]
    )

    try:
        os.makedirs(topic_folder, exist_ok=True)
    except Exception as e:
        logger.error(f"토픽 이미지 폴더 생성 오류: {e}")

    # 스타일 설정
    style_config = {
        "default": {
            "colors": ["#6495ED", "#F4A460", "#90EE90", "#F08080"],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.7,
            "background": "white",
            "figsize": (14, 9),
        },
        "colorful": {
            "colors": [
                "#FF6B6B",
                "#4ECDC4",
                "#FFA726",
                "#66BB6A",
                "#42A5F5",
                "#AB47BC",
            ],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.5,
            "background": "#F8F9FA",
            "figsize": (14, 9),
        },
        "minimal": {
            "colors": ["#555555", "#777777", "#999999", "#BBBBBB"],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.4,
            "background": "white",
            "figsize": (14, 9),
        },
        "dark": {
            "colors": ["#3498DB", "#E74C3C", "#2ECC71", "#F39C12", "#9B59B6"],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.6,
            "background": "#F8F9FA",
            "figsize": (14, 9),
        },
    }

    # 존재하지 않는 스타일이면 기본값 사용
    if chart_style not in style_config:
        chart_style = "default"

    # 현재 스타일 설정
    current_style = style_config[chart_style]
    topic_colors = current_style["colors"]

    for topic_id in range(model.num_topics):
        try:
            # 편집된 토픽 단어 가져오기
            topic_words = get_topic_words_with_edits(model, topic_id, edited_keywords)
            words = [word for word, _ in topic_words[:10]]  # 상위 10개 단어
            weights = [weight for _, weight in topic_words[:10]]

            # 시각화를 위해 데이터 정렬 (가중치가 높은 순)
            indices = np.argsort(weights)[::-1]
            words = [words[i] for i in indices]
            weights = [weights[i] for i in indices]

            # 현재 토픽의 색상 (색상 배열의 범위를 넘어가면 순환)
            color = topic_colors[topic_id % len(topic_colors)]

            # 토픽 시각화 - 스타일 설정 적용
            plt.figure(
                figsize=current_style["figsize"], facecolor=current_style["background"]
            )
            y_pos = np.arange(len(words))

            # 막대 그래프 생성
            bars = plt.barh(y_pos, weights, align="center", color=color)

            # 축 라벨 설정 - 글자 크기 증가
            plt.yticks(y_pos, words, fontsize=current_style["fontsize_ticks"])
            plt.xticks(fontsize=current_style["fontsize_ticks"])
            plt.xlabel(
                "가중치", fontsize=current_style["fontsize_axes"], fontweight="bold"
            )
            plt.ylabel(
                "키워드", fontsize=current_style["fontsize_axes"], fontweight="bold"
            )
            plt.title(
                f"TOPIC {topic_id + 1}",
                fontsize=current_style["fontsize_title"],
                fontweight="bold",
            )

            # 그리드 추가
            plt.grid(axis="x", linestyle="--", alpha=current_style["grid_alpha"])

            # 각 막대 끝에 값 표시 - 글자 크기 증가
            for i, v in enumerate(weights):
                plt.text(
                    v + 0.0005,
                    i,
                    f"{v:.3f}",
                    va="center",
                    fontsize=current_style["fontsize_values"],
                    fontweight="bold",
                )

            plt.tight_layout()

            # 이미지 저장 (캐시 방지를 위한 타임스탬프 추가)
            timestamp = int(random.random() * 10000)
            topic_img_path = os.path.join(
                topic_folder, f"topic_{topic_id + 1}_{timestamp}.png"
            )

            try:
                plt.savefig(topic_img_path, dpi=150, bbox_inches="tight")
                plt.close()

                # 상대 경로 생성 (세션 ID 포함)
                relative_path = "/".join([
                    current_app.config["TOPIC_MODELS_FOLDER"],
                    os.path.basename(topic_img_path),
                ])

                topic_images.append({"id": topic_id, "path": relative_path})
            except Exception as e:
                logger.error(f"토픽 {topic_id + 1} 이미지 저장 오류: {e}")
                plt.close()

                # 오류 발생 시 간단한 이미지 생성 시도
                try:
                    plt.figure(figsize=(10, 6))
                    plt.text(
                        0.5,
                        0.5,
                        f"토픽 {topic_id + 1} 시각화 오류",
                        ha="center",
                        va="center",
                        fontsize=20,
                    )
                    plt.axis("off")

                    err_topic_img_path = os.path.join(
                        topic_folder, f"topic_{topic_id + 1}_error_{timestamp}.png"
                    )
                    plt.savefig(err_topic_img_path, dpi=100)
                    plt.close()

                    relative_path = "/".join([
                        current_app.config["TOPIC_MODELS_FOLDER"],
                        os.path.basename(err_topic_img_path),
                    ])
                    topic_images.append({"id": topic_id, "path": relative_path})
                except Exception as e2:
                    logger.error(f"토픽 {topic_id + 1} 오류 이미지 생성 실패: {e2}")
                    topic_images.append(
                        {"id": topic_id, "path": f"topic_{topic_id + 1}_error.png"}
                    )
        except Exception as e:
            logger.error(f"토픽 {topic_id + 1} 처리 중 오류 발생: {e}")
            # 오류 발생 시에도 결과 목록에 추가
            timestamp = int(random.random() * 10000)
            try:
                plt.figure(figsize=(10, 6))
                plt.text(
                    0.5,
                    0.5,
                    f"토픽 {topic_id + 1} 처리 오류",
                    ha="center",
                    va="center",
                    fontsize=20,
                )
                plt.axis("off")

                err_topic_img_path = os.path.join(
                    topic_folder, f"topic_{topic_id + 1}_error_{timestamp}.png"
                )
                plt.savefig(err_topic_img_path, dpi=100)
                plt.close()

                relative_path = "/".join([
                    current_app.config["TOPIC_MODELS_FOLDER"],
                    os.path.basename(err_topic_img_path),
                ])
                topic_images.append({"id": topic_id, "path": relative_path})
            except Exception as e2:
                logger.error(f"토픽 {topic_id + 1} 오류 이미지 생성 실패: {e2}")
                topic_images.append(
                    {"id": topic_id, "path": f"topic_{topic_id + 1}_error.png"}
                )

    return topic_images


# (6) LDA 분석 실행 및 시각화 함수
# (6-1) LDA 분석 메인 함수
def run_lda_analysis(
    file_path,
    text_column,
    min_topic=3,
    max_topic=10,
    no_below=5,
    no_above=0.2,
    network_style="academic",
    manual_topic_number=None,
    chart_style="default",
    fast_mode=False,
):
    """
    LDA 토픽 모델링 분석 함수

    Args:
        fast_mode (bool): False이면 전체 모드 (모든 차트 생성, 기본값), True이면 빠른 모드 (일부 차트 생략)
    """
    global global_model, global_corpus, global_dictionary, global_tokens

    # Get current session ID
    session_id = get_current_session_id()

    # 필요한 디렉토리 생성 확인
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    analysis_folder = os.path.join(session_folder, "lda_analysis")
    topic_folder = os.path.join(
        analysis_folder, current_app.config["TOPIC_MODELS_FOLDER"]
    )
    word_cloud_folder = os.path.join(
        analysis_folder, current_app.config["WORD_CLOUD_FOLDER"]
    )
    for directory in [topic_folder, word_cloud_folder]:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
            except Exception:
                # 오류 발생 시에도 계속 진행 (다른 디렉토리는 이미 존재할 수 있음)
                pass

    try:
        # (6-1-1) 파일 로드 및 데이터 전처리
        # (6-1-1-1) 파일 타입에 따라 데이터 로드

        data = read_file(file_path)

        # (6-1-1-2) 데이터를 문자열로 변환
        data = data.astype("str")

        # 컬럼 존재 여부 확인
        if text_column not in data.columns:
            raise ValueError(
                f"선택한 텍스트 컬럼 '{text_column}'이 데이터에 존재하지 않습니다."
            )

        text_data = data[text_column]

        # 빈 데이터 확인
        if text_data.empty:
            raise ValueError("텍스트 데이터가 비어 있습니다.")

        # (6-1-1-3) 텍스트 데이터 전처리: 특수문자 및 기호 제거 (최적화된 버전)
        # 단일 정규식으로 모든 전처리 수행
        text_data = text_data.str.replace(
            pat=r"[^\w\s가-힣]", repl=" ", regex=True
        )  # 한글, 영문, 숫자, 공백만 유지
        text_data = text_data.str.replace(
            pat=r"\s+", repl=" ", regex=True
        ).str.strip()  # 연속 공백 제거

        # 데이터 길이 확인
        if text_data.nunique() < 2:
            raise ValueError("고유 문서가 2개 미만입니다. 다양한 문서가 필요합니다.")

        # (6-1-2) 토큰화 및 모델 학습 데이터 준비
        # (6-1-2-1) 텍스트 토큰화
        tokens = [text.split() for text in text_data]
        global_tokens = tokens

        # 빈 토큰 제거
        tokens = [token for token in tokens if len(token) > 0]
        if len(tokens) < 2:
            raise ValueError(
                "토큰화 후 유효한 문서가 2개 미만입니다. 다른 텍스트 컬럼을 선택하거나 전처리 방법을 변경해보세요."
            )

        # (6-1-2-2) 단어 사전 생성
        id2word = corpora.Dictionary(tokens)

        # (6-1-2-3) 빈도 기반 단어 필터링
        id2word.filter_extremes(no_below=no_below, no_above=no_above)
        global_dictionary = id2word

        # 단어 사전 확인
        if len(id2word) < 10:
            # 필터링 설정 완화 시도
            no_below = max(2, no_below // 2)
            no_above = min(0.5, no_above * 1.5)
            id2word = corpora.Dictionary(tokens)
            id2word.filter_extremes(no_below=no_below, no_above=no_above)
            global_dictionary = id2word

        # (6-1-2-4) 말뭉치(Corpus) 생성 - 각 문서를 BOW(Bag of Words) 형태로 변환
        corpus = [id2word.doc2bow(text) for text in tokens]
        global_corpus = corpus

        # 빈 문서 제거
        corpus = [doc for doc in corpus if len(doc) > 0]
        global_corpus = corpus

        if len(corpus) == 0:
            raise ValueError(
                "유효한 말뭉치가 생성되지 않았습니다. 필터링 설정을 조정해보세요."
            )

        if len(id2word) == 0:
            raise ValueError(
                "유효한 단어 사전이 생성되지 않았습니다. 필터링 설정을 조정해보세요."
            )

        # (6-1-3) 최적 토픽 수 선정 - 성능 최적화
        # 토픽 범위 조정 (문서 수가 적을 경우)
        max_reasonable_topics = min(
            max_topic, len(corpus) // 5
        )  # 문서 수의 1/5를 최대 토픽 수로 제한
        max_topic = max(
            min_topic + 1, max_reasonable_topics
        )  # 최소한 min_topic + 1은 되게

        if fast_mode or manual_topic_number is not None:
            # 빠른 모드: 평가 단계 건너뛰고 기본값 또는 수동 지정값 사용
            if manual_topic_number is not None:
                used_topic_num = manual_topic_number
            else:
                # 데이터 크기에 따른 기본 토픽 수 추정
                if len(corpus) < 50:
                    used_topic_num = min(3, max_topic)
                elif len(corpus) < 200:
                    used_topic_num = min(5, max_topic)
                else:
                    used_topic_num = min(7, max_topic)

            # 더미 값으로 그래프 생성 - 평가 범위 설정
            eval_max_topic = max_topic
            topic_range = list(range(min_topic, eval_max_topic + 1))
            perplexity_values = [100.0 - i * 5 for i in topic_range]  # 감소하는 더미 값
            coherence_values = [
                0.3 + i * 0.05 for i in range(len(topic_range))
            ]  # 증가하는 더미 값
            optimal_topic_num = used_topic_num

        else:
            # 전체 평가 모드: 성능 최적화된 평가
            perplexity_values = []
            coherence_values = []

            # 평가 범위를 줄여서 성능 향상 (최대 5개 토픽까지만 평가)
            eval_max_topic = min(max_topic, min_topic + 4)

            # 단일 루프로 perplexity와 coherence 동시 계산 (성능 최적화)
            for i in range(min_topic, eval_max_topic + 1):
                try:
                    # 더 적은 passes로 빠른 모델 학습
                    ldamodel = LdaModel(
                        corpus,
                        num_topics=i,
                        id2word=id2word,
                        passes=3,
                        random_state=100,
                    )

                    # Perplexity 계산
                    perplexity = np.exp(ldamodel.log_perplexity(corpus))
                    perplexity_values.append(float(perplexity))

                    # Coherence 계산 (topn을 줄여서 성능 향상)
                    coherence_model = CoherenceModel(
                        model=ldamodel, texts=tokens, dictionary=id2word, topn=5
                    )
                    coherence = coherence_model.get_coherence()
                    coherence_values.append(coherence)

                except Exception:
                    # 오류 발생 시 더미 값 추가
                    perplexity_values.append(
                        1000.0
                        if len(perplexity_values) == 0
                        else perplexity_values[-1] * 1.05
                    )
                    coherence_values.append(
                        0.0
                        if len(coherence_values) == 0
                        else coherence_values[-1] * 0.95
                    )

            # 최적 토픽 개수 찾기
            if len(coherence_values) > 0:
                optimal_topic_idx = np.argmax(coherence_values)
                optimal_topic_num = min_topic + optimal_topic_idx
            else:
                optimal_topic_num = (min_topic + eval_max_topic) // 2

            used_topic_num = optimal_topic_num

        # (6-1-4) 평가 시각화 그래프 생성
        # (6-1-4-1) 혼잡도 그래프 생성
        try:
            x_range = list(range(min_topic, eval_max_topic + 1))
            # 데이터 길이 확인 및 조정
            if len(perplexity_values) != len(x_range):
                perplexity_values = (
                    perplexity_values[: len(x_range)]
                    if len(perplexity_values) > len(x_range)
                    else perplexity_values
                    + [perplexity_values[-1]] * (len(x_range) - len(perplexity_values))
                )

            perplexity_img = create_plot(
                x_range,
                perplexity_values,
                "Number of Topics",
                "Perplexity Score",
                "Perplexity Score by Number of Topics",
            )
        except Exception as e:
            # 오류 발생 시 기본 그래프 생성
            perplexity_img = create_plot(
                [min_topic],
                [100.0],
                "Number of Topics",
                "Perplexity Score",
                "Perplexity Score by Number of Topics",
            )

        # (6-1-4-2) 일관성 그래프 생성
        try:
            x_range = list(range(min_topic, eval_max_topic + 1))
            # 데이터 길이 확인 및 조정
            if len(coherence_values) != len(x_range):
                coherence_values = (
                    coherence_values[: len(x_range)]
                    if len(coherence_values) > len(x_range)
                    else coherence_values
                    + [coherence_values[-1]] * (len(x_range) - len(coherence_values))
                )

            coherence_img = create_plot(
                x_range,
                coherence_values,
                "Number of Topics",
                "Coherence Score",
                "Coherence Score by Number of Topics",
            )
        except Exception as e:
            # 오류 발생 시 기본 그래프 생성
            coherence_img = create_plot(
                [min_topic],
                [0.3],
                "Number of Topics",
                "Coherence Score",
                "Coherence Score by Number of Topics",
            )

        # 토픽 수 유효성 검증
        used_topic_num = max(
            2, min(used_topic_num, len(corpus) // 2)
        )  # 최소 2개, 최대 문서 수의 절반

        # (6-1-5) 최종 모델 학습 - 성능 최적화
        try:
            # 성능 최적화: passes 수를 줄이고 다른 파라미터 조정
            if fast_mode:
                # 빠른 모드: 최소한의 passes로 빠른 학습
                passes = 5
                iterations = 50
            else:
                # 정확도 모드: 더 많은 passes로 정확한 학습
                passes = 10
                iterations = 100

            final_model = LdaModel(
                corpus=corpus,
                num_topics=used_topic_num,
                id2word=id2word,
                passes=passes,
                iterations=iterations,
                random_state=100,
                alpha="auto",  # 자동 alpha 조정으로 성능 향상
                per_word_topics=True,  # 단어별 토픽 확률 계산
            )
            global_model = final_model
            global_corpus = corpus
            global_dictionary = id2word

            # Save the trained model and data
            save_lda_model(final_model, session_id, corpus=corpus, dictionary=id2word)

        except Exception as e:
            # 모델 학습에 실패한 경우 더 간단한 모델로 시도
            try:
                used_topic_num = max(2, used_topic_num // 2)
                final_model = LdaModel(
                    corpus=corpus,
                    num_topics=used_topic_num,
                    id2word=id2word,
                    passes=3,
                    iterations=30,
                    random_state=100,
                )
                global_model = final_model
                global_corpus = corpus
                global_dictionary = id2word

                # Save the simpler model and data
                save_lda_model(
                    final_model, session_id, corpus=corpus, dictionary=id2word
                )

            except Exception:
                raise ValueError(
                    "LDA 모델 학습에 실패했습니다. 데이터를 확인하고 다시 시도해주세요."
                )

        # (6-1-6) 토픽 정보 추출
        try:
            topics = final_model.print_topics(num_words=15)
            topics_formatted = []

            for topic_id, topic_words in topics:
                topics_formatted.append({"id": int(topic_id), "words": topic_words})
        except Exception:
            # 기본 토픽 정보 생성
            topics_formatted = []
            for topic_id in range(used_topic_num):
                topics_formatted.append(
                    {
                        "id": topic_id,
                        "words": f'{0.1}*"word1" + {0.09}*"word2" + {0.08}*"word3"',
                    }
                )

        # (6-1-7) 토픽별 주요 단어 추출
        discriminative_words = {}
        for topic_id in range(used_topic_num):
            try:
                # 토픽의 상위 단어와 가중치 가져오기
                topic_words = final_model.show_topic(
                    topic_id, topn=20
                )  # 워드 클라우드를 위해 단어 수 늘림
                discriminative_words[topic_id] = topic_words
            except Exception:
                # 더미 데이터 생성
                discriminative_words[topic_id] = [
                    ("word1", 0.1),
                    ("word2", 0.09),
                    ("word3", 0.08),
                ]

        # (6-1-8) 다양한 시각화 결과물 생성 - 성능 최적화
        # 빠른 모드에서는 필수 시각화만 생성

        # (6-1-8-1) 개별 토픽별 워드 클라우드 이미지 생성 (항상 생성)
        try:
            if fast_mode:
                # 빠른 모드: 간단한 워드클라우드만 생성
                wordcloud_paths = create_simple_wordclouds(discriminative_words)
            else:
                # 정확도 모드: 전체 워드클라우드 생성
                wordcloud_paths = create_individual_wordclouds(discriminative_words)
        except Exception:
            # 기본 워드클라우드 이미지 파일 경로들 생성
            wordcloud_paths = []
            for topic_id in range(used_topic_num):
                wordcloud_filename = f"topic_{topic_id + 1}_default_wordcloud.png"
                relative_path = "/".join([
                    current_app.config["WORD_CLOUD_FOLDER"], wordcloud_filename
                ])
                wordcloud_paths.append(
                    {
                        "topic_id": topic_id,
                        "path": relative_path,
                        "filename": wordcloud_filename,
                    }
                )

        # (6-1-8-2) pyLDAvis 시각화 데이터 생성 (조건부)
        if fast_mode:
            # 빠른 모드: pyLDAvis 건너뛰기
            vis_html = "<div class='alert alert-info'>빠른 모드에서는 pyLDAvis 시각화가 생략됩니다.</div>"
        else:
            try:
                # Check if model and data are valid
                if final_model is None:
                    vis_html = (
                        "<div class='alert alert-warning'>LDA 모델이 없습니다.</div>"
                    )
                elif len(corpus) == 0:
                    vis_html = (
                        "<div class='alert alert-warning'>말뭉치가 비어 있습니다.</div>"
                    )
                elif len(id2word) == 0:
                    vis_html = (
                        "<div class='alert alert-warning'>사전이 비어 있습니다.</div>"
                    )
                else:
                    # Ensure we're using the filtered corpus
                    filtered_corpus = [doc for doc in corpus if len(doc) > 0]
                    if len(filtered_corpus) == 0:
                        vis_html = "<div class='alert alert-warning'>유효한 문서가 없습니다.</div>"
                    else:
                        # Prepare visualization with filtered data
                        vis_data = pyLDAvis.gensim.prepare(
                            final_model,
                            filtered_corpus,
                            id2word,
                            sort_topics=False,
                            mds="mmds",  # Use faster MDS algorithm
                        )
                        if vis_data is None:
                            vis_html = "<div class='alert alert-warning'>시각화 데이터를 생성할 수 없습니다.</div>"
                        else:
                            vis_html = pyLDAvis.prepared_data_to_html(vis_data)
            except Exception as e:
                logger.error(f"pyLDAvis 시각화 생성 오류: {str(e)}")
                vis_html = f"<div class='alert alert-warning'>pyLDAvis 시각화 생성 실패: {str(e)}</div>"

        # (6-1-8-3) 각 토픽별 시각화를 PNG 파일로 저장 (조건부)
        if fast_mode:
            # 빠른 모드: 간단한 토픽 이미지만 생성
            topic_images = []
            for topic_id in range(used_topic_num):
                topic_images.append(
                    {
                        "id": topic_id,
                        "path": f"topic_models/simple_topic_{topic_id + 1}.png",
                    }
                )
        else:
            try:
                topic_images = create_topic_visualizations(
                    final_model, used_topic_num, chart_style
                )
            except Exception:
                # 기본 토픽 이미지 경로 생성
                topic_images = []
                for topic_id in range(used_topic_num):
                    topic_images.append(
                        {
                            "id": topic_id,
                            "path": f"topic_models/default_topic_{topic_id + 1}.png",
                        }
                    )

        # (6-1-8-4) 결과 CSV 파일 저장
        try:
            topics_df = pd.DataFrame({"topics": [t[1] for t in topics]})
            csv_path = os.path.join(topic_folder, "lda_result.csv")
            topics_df.to_csv(csv_path, encoding="utf-8")
        except Exception:
            csv_path = os.path.join(topic_folder, "lda_result.csv")

        # (6-1-8-5) 토픽 네트워크 시각화 생성 (조건부)
        if fast_mode:
            # 빠른 모드: 네트워크 시각화 건너뛰기
            network_img_path = "topic_models/simple_network.png"
        else:
            try:
                network_img_path = create_topic_network(
                    final_model, used_topic_num, network_style=network_style
                )
            except Exception:
                network_img_path = "topic_models/default_network.png"

        # (6-1-9) 결과 반환

        return {
            "perplexity_plot": perplexity_img,  # 혼잡도 그래프 (base64 인코딩)
            "coherence_plot": coherence_img,  # 일관성 그래프 (base64 인코딩)
            "optimal_topic_num": int(optimal_topic_num),  # 최적 토픽 개수
            "used_topic_num": int(used_topic_num),  # 실제 사용된 토픽 개수
            "topics": topics_formatted,  # 토픽 정보
            "pyldavis_html": vis_html,  # pyLDAvis 시각화 HTML
            "csv_path": os.path.join(
                *csv_path.split(os.sep)[-2:]
            ),  # csv_path,  # CSV 파일 경로
            "topic_images": topic_images,  # 토픽별 이미지 경로
            "network_img_path": os.path.join(
                *network_img_path.split(os.sep)[-2:]
            ),  # 네트워크 시각화 이미지 경로
            "network_style": network_style,  # 사용된 네트워크 스타일
            "chart_style": chart_style,  # 차트 스타일
            "wordcloud_paths": wordcloud_paths,  # 개별 토픽별 워드 클라우드 이미지 경로 리스트
        }
    except Exception as e:
        raise ValueError(f"LDA 토픽 모델링 처리 중 오류가 발생했습니다: {str(e)}")


def run_lda_analysis_with_edits(
    file_path,
    existing_model,
    existing_corpus,
    existing_dictionary,
    edited_keywords,
    network_style="academic",
    chart_style="default",
    session_id=None,
):
    """
    LDA 분석 결과를 편집된 키워드로 재생성 (모델 재학습 없이 시각화만 업데이트)

    Args:
        file_path: 원본 파일 경로 (일부 시각화 함수에서 필요)
        existing_model: 기존 학습된 LDA 모델
        existing_corpus: 기존 코퍼스
        existing_dictionary: 기존 사전
        edited_keywords: 편집된 키워드 정보
        network_style: 네트워크 시각화 스타일
        chart_style: 차트 스타일
        session_id: 세션 ID

    Returns:
        dict: run_lda_analysis와 동일한 구조의 결과
    """
    try:
        if session_id is None:
            session_id = get_current_session_id()

        # 결과 폴더 설정
        result_folder = current_app.config["RESULT_FOLDER"]
        session_folder = os.path.join(result_folder, session_id)
        topic_folder = os.path.join(
            session_folder, current_app.config["TOPIC_MODELS_FOLDER"]
        )
        os.makedirs(topic_folder, exist_ok=True)

        # 편집된 키워드로 토픽 정보 업데이트
        updated_topics = get_updated_topics(existing_model, edited_keywords)
        used_topic_num = len(updated_topics)

        # updated_topics는 이미 올바른 형식 [{'id': int, 'words': str}, ...]
        # run_lda_analysis와 동일한 형식으로 사용
        topics_formatted = updated_topics

        # pyLDAvis 시각화 생성 (편집된 키워드 반영)
        try:
            vis_html = create_updated_pyldavis(
                existing_model, existing_corpus, existing_dictionary, edited_keywords
            )
        except Exception as e:
            logger.error(f"pyLDAvis 시각화 생성 오류: {str(e)}")
            vis_html = f"<div class='alert alert-warning'>pyLDAvis 시각화 생성 실패: {str(e)}</div>"

        # 토픽별 시각화 이미지 생성 (편집된 키워드 반영)
        try:
            topic_images = update_topic_visualizations(
                existing_model, edited_keywords, chart_style
            )
        except Exception as e:
            logger.error(f"토픽 시각화 생성 오류: {str(e)}")
            # 기본 토픽 이미지 경로 생성
            topic_images = []
            for topic_id in range(used_topic_num):
                topic_images.append(
                    {
                        "id": topic_id,
                        "path": f"topic_models/default_topic_{topic_id + 1}.png",
                    }
                )

        # 네트워크 시각화 생성 (편집된 키워드 반영)
        try:
            network_img_path = create_topic_network(
                existing_model,
                used_topic_num,
                network_style=network_style,
                edited_keywords=edited_keywords,
            )
        except Exception as e:
            logger.error(f"네트워크 시각화 생성 오류: {str(e)}")
            network_img_path = "topic_models/default_network.png"

        # 개별 토픽별 워드클라우드 생성 (편집된 키워드 반영)
        try:
            discriminative_words = {}
            for topic_id in range(used_topic_num):
                topic_words = get_topic_words_with_edits(
                    existing_model, topic_id, edited_keywords
                )
                discriminative_words[topic_id] = topic_words[:30]  # 상위 30개 단어 사용

            wordcloud_paths = create_individual_wordclouds(discriminative_words)
        except Exception as e:
            logger.error(f"워드클라우드 생성 오류: {str(e)}")
            wordcloud_paths = []

        # CSV 파일 저장
        try:
            topics_df = pd.DataFrame({"topics": [t["words"] for t in topics_formatted]})
            csv_path = os.path.join(topic_folder, "lda_result_edited.csv")
            topics_df.to_csv(csv_path, encoding="utf-8")
            csv_relative_path = os.path.join(*csv_path.split(os.sep)[-2:])
        except Exception as e:
            logger.error(f"CSV 파일 저장 오류: {str(e)}")
            csv_relative_path = "topic_models/lda_result_edited.csv"

        # run_lda_analysis와 동일한 구조로 결과 반환
        return {
            "perplexity_plot": None,  # 편집 모드에서는 해당 없음 (프론트엔드 일관성을 위해 포함)
            "coherence_plot": None,  # 편집 모드에서는 해당 없음 (프론트엔드 일관성을 위해 포함)
            "optimal_topic_num": used_topic_num,  # 현재 토픽 수 사용
            "used_topic_num": used_topic_num,
            "topics": topics_formatted,
            "pyldavis_html": vis_html,
            "csv_path": csv_relative_path,
            "topic_images": topic_images,
            "network_img_path": (
                os.path.join(*network_img_path.split(os.sep)[-2:])
                if network_img_path
                else None
            ),
            "network_style": network_style,
            "chart_style": chart_style,
            "wordcloud_paths": wordcloud_paths,
        }

    except Exception as e:
        logger.error(f"편집된 키워드로 LDA 분석 재생성 중 오류: {str(e)}")
        raise ValueError(
            f"편집된 키워드로 분석 재생성 중 오류가 발생했습니다: {str(e)}"
        )


# (7) 시각화 그래프 생성 함수
# (7-1) 일관성과 혼잡도 그래프 생성 함수
def create_plot(x_values, y_values, xlabel, ylabel, title):
    # (7-1-1) 그래프 초기화 및 데이터 표시
    plt.figure(figsize=(10, 6))
    plt.plot(x_values, y_values, marker="o", color="black", markerfacecolor="black")
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.title(title)

    # (7-1-2) 그래프에 값 표시
    for i, value in enumerate(y_values):
        plt.text(x_values[i], value, f"{value:.5f}", ha="center", va="bottom")

    # (7-1-3) 이미지를 base64로 인코딩하여 반환
    buffer = BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_png = buffer.getvalue()
    buffer.close()
    return base64.b64encode(image_png).decode("utf-8")


# (7-2) 워드 클라우드 시각화 함수
def create_wordcloud_visualization(discriminative_words):
    # 저장 폴더가 없으면 생성
    session_id = get_current_session_id()
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    topic_folder = os.path.join(
        session_folder, current_app.config["TOPIC_MODELS_FOLDER"]
    )
    wordcloud_images = os.path.join(
        session_folder, current_app.config["WORD_CLOUD_FOLDER"]
    )

    # wordcloud_images  = os.path.join(current_app.config['RESULT_FOLDER'], current_app.config['WORD_CLOUD_FOLDER'])
    try:
        os.makedirs(wordcloud_images, exist_ok=True)
    except Exception:
        pass

    # (7-2-1) 색상 팔레트 및 기타 설정
    colors = [
        "#66c2a5",
        "#fc8d62",
        "#8da0cb",
        "#e78ac3",
        "#a6d854",
        "#ffd92f",
        "#e5c494",
        "#b3b3b3",
    ]

    # (7-2-2) 겹치는 단어 찾기
    word_occurrences = defaultdict(list)
    for topic_id, words_with_weights in discriminative_words.items():
        for word, _ in words_with_weights:
            word_occurrences[word].append(topic_id)

    # (7-2-3) 폰트 경로 설정
    font_path = None
    # 운영체제별 기본 한글 폰트 경로 확인
    if os.name == "nt":  # 윈도우
        font_path = current_app.config["FONT_PATH"]
    elif os.name == "posix":  # macOS 또는 Linux
        if os.path.exists("/Library/Fonts/AppleGothic.ttf"):  # macOS
            font_path = "/Library/Fonts/AppleGothic.ttf"
        else:  # Linux
            # 리눅스에서 일반적인 한글 폰트 경로들
            possible_paths = [
                "/usr/share/fonts/truetype/nanum/NanumGothic.ttf",
                "/usr/share/fonts/nanum/NanumGothic.ttf",
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    font_path = path
                    break

    # 만약 시스템에서 폰트를 찾지 못한 경우 기본 폰트 사용
    if not font_path or not os.path.exists(font_path):
        print("한글 폰트를 찾을 수 없어 기본 폰트를 사용합니다.")

    # (7-2-5) 각 토픽별로 워드 클라우드 배치하여 시각화
    rows = len(discriminative_words) // 3 + (
        1 if len(discriminative_words) % 3 > 0 else 0
    )
    fig, axes = plt.subplots(rows, 3, figsize=(18, rows * 6))
    if rows == 1 and len(discriminative_words) == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes.flatten() if hasattr(axes, "flatten") else axes
    else:
        axes = axes.flatten()

    # (7-2-6) 각 토픽별 워드 클라우드 생성
    for idx, (topic_id, words_with_weights) in enumerate(discriminative_words.items()):
        if idx >= len(axes):
            print(f"경고: 토픽 {topic_id}를 표시할 충분한 서브플롯이 없습니다.")
            continue

        words, weights = zip(*words_with_weights)
        word_frequencies = dict(zip(words, weights))

        try:
            # (7-2-6-1) 워드 클라우드 객체 생성 및 데이터 설정
            wordcloud = WordCloud(
                font_path=font_path,
                width=400,
                height=300,
                background_color="white",
                colormap="viridis",
            ).generate_from_frequencies(word_frequencies)

            # (7-2-6-2) 해당 서브플롯에 워드 클라우드 표시
            axes[idx].imshow(wordcloud, interpolation="bilinear")
            axes[idx].set_title(f"Topic {topic_id + 1}", fontsize=16, fontweight="bold")
            axes[idx].axis("off")
        except Exception as e:
            print(f"워드 클라우드 생성 중 오류 발생: {e}")
            axes[idx].text(
                0.5,
                0.5,
                f"워드 클라우드 생성 오류: {e}",
                ha="center",
                va="center",
                fontsize=12,
            )
            axes[idx].axis("off")

    # (7-2-7) 전체 서브플롯에서 사용하지 않는 축을 비활성화
    num_topics = len(discriminative_words)
    for i in range(num_topics, len(axes)):
        axes[i].axis("off")

    plt.tight_layout()

    # (7-2-8) 이미지 저장 및 경로 반환
    timestamp = int(random.random() * 10000)
    wordcloud_filename = f"wordcloud_{timestamp}.png"
    wordcloud_img_path = os.path.join(wordcloud_images, wordcloud_filename)

    try:
        plt.savefig(wordcloud_img_path, dpi=150, bbox_inches="tight")
        plt.close()
    except Exception:
        # 오류 발생 시 더 간단한 이미지로 시도
        plt.figure(figsize=(8, 6))
        plt.text(
            0.5, 0.5, "워드클라우드 생성 오류", ha="center", va="center", fontsize=20
        )
        plt.axis("off")
        try:
            plt.savefig(wordcloud_img_path, dpi=100)
            plt.close()
        except Exception:
            # 오류 발생 시 기본 오류 이미지 경로 설정
            wordcloud_filename = "wordcloud_error.png"
            wordcloud_img_path = os.path.join(wordcloud_images, wordcloud_filename)
            # 기본 오류 이미지 생성 시도
            try:
                plt.figure(figsize=(8, 6))
                plt.text(
                    0.5,
                    0.5,
                    "워드클라우드 생성 오류",
                    ha="center",
                    va="center",
                    fontsize=20,
                )
                plt.axis("off")
                plt.savefig(wordcloud_img_path, dpi=100)
                plt.close()
            except Exception:
                pass

    # 상대 경로 반환 (세션 ID 포함)
    return os.path.join(*wordcloud_img_path.split(os.sep)[-2:])


# (7-2-NEW) 간단한 워드클라우드 생성 함수 (성능 최적화)
def create_simple_wordclouds(discriminative_words):
    """각 토픽별로 간단한 워드클라우드 파일을 빠르게 생성"""
    session_id = get_current_session_id()
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    wordcloud_images = os.path.join(
        session_folder, current_app.config["WORD_CLOUD_FOLDER"]
    )

    try:
        os.makedirs(wordcloud_images, exist_ok=True)
    except Exception:
        pass

    wordcloud_paths = []
    timestamp = int(random.random() * 10000)

    # 각 토픽별로 간단한 워드클라우드 생성
    for topic_id, words_with_weights in discriminative_words.items():
        try:
            # 상위 10개 단어만 사용 (성능 최적화)
            top_words = words_with_weights[:10]
            words, weights = zip(*top_words)
            word_frequencies = dict(zip(words, weights))

            # 워드클라우드 파일명 생성
            wordcloud_filename = (
                f"topic_{topic_id + 1}_simple_wordcloud_{timestamp}.png"
            )
            wordcloud_img_path = os.path.join(wordcloud_images, wordcloud_filename)

            # 간단한 워드 클라우드 생성 (성능 최적화된 파라미터)
            wordcloud = WordCloud(
                background_color="white",
                width=400,  # 작은 크기
                height=300,  # 작은 크기
                max_words=10,  # 최대 단어 수 제한
                colormap="viridis",
            ).generate_from_frequencies(word_frequencies)

            # 이미지 저장
            wordcloud.to_file(wordcloud_img_path)

            # 상대 경로로 변환하여 리스트에 추가
            relative_path = os.path.join(*wordcloud_img_path.split(os.sep)[-2:])
            wordcloud_paths.append(
                {
                    "topic_id": topic_id,
                    "path": relative_path,
                    "filename": wordcloud_filename,
                }
            )

        except Exception as e:
            # 오류 발생 시 기본 경로 추가
            wordcloud_paths.append({"topic_id": topic_id, "path": "", "filename": ""})

    return wordcloud_paths


# (7-2-FULL) 개별 토픽별 워드클라우드 생성 함수
def create_individual_wordclouds(discriminative_words, origin_analysis=None):
    """각 토픽별로 개별 워드클라우드 파일을 생성하고 경로 리스트를 반환"""
    # 저장 폴더가 없으면 생성
    session_id = get_current_session_id()
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    if origin_analysis:
        wordcloud_images = os.path.join(
            session_folder, current_app.config["WORD_CLOUD_FOLDER"]
        )
    else:
        analysis_folder = os.path.join(session_folder, "lda_analysis")
        wordcloud_images = os.path.join(
            analysis_folder, current_app.config["WORD_CLOUD_FOLDER"]
        )

    try:
        os.makedirs(wordcloud_images, exist_ok=True)
    except Exception:
        pass

    # 폰트 경로 설정
    font_path = None
    # 운영체제별 기본 한글 폰트 경로 확인
    if os.name == "nt":  # 윈도우
        font_path = current_app.config["FONT_PATH"]
    elif os.name == "posix":  # macOS 또는 Linux
        if os.path.exists("/Library/Fonts/AppleGothic.ttf"):  # macOS
            font_path = "/Library/Fonts/AppleGothic.ttf"
        else:  # Linux
            # 리눅스에서 일반적인 한글 폰트 경로들
            possible_paths = [
                "/usr/share/fonts/truetype/nanum/NanumGothic.ttf",
                "/usr/share/fonts/nanum/NanumGothic.ttf",
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    font_path = path
                    break

    # 만약 시스템에서 폰트를 찾지 못한 경우 기본 폰트 사용
    if not font_path or not os.path.exists(font_path):
        print("한글 폰트를 찾을 수 없어 기본 폰트를 사용합니다.")

    wordcloud_paths = []
    timestamp = int(random.random() * 10000)

    # 각 토픽별로 개별 워드클라우드 생성
    for topic_id, words_with_weights in discriminative_words.items():
        try:
            words, weights = zip(*words_with_weights)
            word_frequencies = dict(zip(words, weights))

            # 워드클라우드 파일명 생성
            wordcloud_filename = f"topic_{topic_id + 1}_wordcloud_{timestamp}.png"
            wordcloud_img_path = os.path.join(wordcloud_images, wordcloud_filename)

            # 워드 클라우드 객체 생성 및 데이터 설정
            wordcloud = WordCloud(
                font_path=font_path,
                width=800,
                height=600,
                background_color="white",
                colormap="viridis",
                max_words=100,
            ).generate_from_frequencies(word_frequencies)

            # 개별 이미지로 저장
            wordcloud.to_file(wordcloud_img_path)

            # 상대 경로로 변환하여 리스트에 추가
            relative_path = os.path.join(*wordcloud_img_path.split(os.sep)[-2:])
            wordcloud_paths.append(
                {
                    "topic_id": topic_id,
                    "path": relative_path,
                    "filename": wordcloud_filename,
                }
            )

        except Exception as e:
            print(f"토픽 {topic_id + 1} 워드클라우드 생성 중 오류 발생: {e}")
            # 오류 발생 시 기본 이미지 생성
            try:
                wordcloud_filename = (
                    f"topic_{topic_id + 1}_wordcloud_error_{timestamp}.png"
                )
                wordcloud_img_path = os.path.join(wordcloud_images, wordcloud_filename)

                plt.figure(figsize=(8, 6))
                plt.text(
                    0.5,
                    0.5,
                    f"Topic {topic_id + 1}\n워드클라우드 생성 오류",
                    ha="center",
                    va="center",
                    fontsize=16,
                )
                plt.axis("off")
                plt.savefig(wordcloud_img_path, dpi=100, bbox_inches="tight")
                plt.close()

                relative_path = os.path.join(*wordcloud_img_path.split(os.sep)[-2:])
                wordcloud_paths.append(
                    {
                        "topic_id": topic_id,
                        "path": relative_path,
                        "filename": wordcloud_filename,
                    }
                )
            except Exception:
                # 완전히 실패한 경우 빈 경로 추가
                wordcloud_paths.append(
                    {"topic_id": topic_id, "path": "", "filename": ""}
                )

    return wordcloud_paths


# (7-3) 토픽-키워드 네트워크 시각화 함수
def create_topic_network(
    lda_model,
    num_topics,
    max_keywords=8,
    network_style="academic",
    edited_keywords=None,
):
    # 저장 폴더가 없으면 생성
    session_id = get_current_session_id()
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    analysis_folder = os.path.join(session_folder, "lda_analysis")
    topic_folder = os.path.join(
        analysis_folder, current_app.config["TOPIC_MODELS_FOLDER"]
    )
    # word_cloud_folder = os.path.join(analysis_folder, current_app.config['WORD_CLOUD_FOLDER'])

    try:
        os.makedirs(topic_folder, exist_ok=True)
    except Exception:
        pass

    # (7-3-1) 그래프 객체 초기화
    G = nx.Graph()

    # (7-3-2) 네트워크 데이터 준비
    topic_nodes = []
    keyword_nodes = []
    keyword_weights = {}
    topic_keyword_map = {}
    all_weights = []

    # (7-3-3) 토픽별 데이터 가공 및 수집
    topic_data = []
    for topic_id in range(num_topics):
        # (7-3-3-1) 편집된 키워드 반영
        if edited_keywords and topic_id in edited_keywords:
            topic_words = get_topic_words_with_edits(
                lda_model, topic_id, edited_keywords
            )
            # max_keywords 개수만큼 자르기
            topic_words = topic_words[:max_keywords]
        else:
            # 토픽의 상위 단어와 가중치 가져오기
            try:
                topic_words = lda_model.show_topic(topic_id, topn=max_keywords)
            except Exception as e:
                print(f"토픽 {topic_id}의 단어 가져오기 오류: {e}")
                topic_words = [
                    ("word" + str(i), 0.1 / (i + 1)) for i in range(max_keywords)
                ]

        words = [word for word, _ in topic_words]
        weights = [float(weight) for _, weight in topic_words]

        # (7-3-3-2) 데이터 저장
        for word, weight in zip(words, weights):
            topic_data.append({"topic": topic_id, "word": word, "weight": weight})
            all_weights.append(weight)

    # (7-3-4) 토픽 데이터프레임 생성
    topic_df = pd.DataFrame(topic_data)

    # (7-3-5) 가중치 정규화 준비 (전체 범위 계산)
    min_weight = min(all_weights) if all_weights else 0
    max_weight = max(all_weights) if all_weights else 1
    weight_range = max_weight - min_weight

    # (7-3-6) 네트워크 시각화 스타일 설정
    # (7-3-6-1) 다양한 스타일 설정 정의
    style_configs = {
        "default": {
            "topic_color": "#3182bd",
            "keyword_color": "#e6550d",
            "edge_color": "#666666",
            "background_color": "white",
            "node_size_factor": 1.0,
            "edge_width_factor": 1.0,
            "layout_type": "grid",
            "font_size_topic": 40,
            "font_size_keyword_min": 30,
            "font_size_keyword_max": 40,
            "adjust_text": True,
        },
        "academic": {
            "topic_color": "#1f77b4",  # 좀 더 짙은 파란색
            "keyword_color": "#d62728",  # 학술적인 느낌의 적색
            "edge_color": "#333333",  # 선명한 엣지
            "background_color": "white",
            "node_size_factor": 0.9,  # 조금 작은 노드
            "edge_width_factor": 0.8,  # 가는 선
            "layout_type": "grid",
            "font_size_topic": 36,  # 좀 더 작은 폰트
            "font_size_keyword_min": 24,
            "font_size_keyword_max": 30,
            "adjust_text": True,  # 텍스트 위치 최적화
            "use_bbox": True,  # 텍스트 주변 박스 사용
        },
        "publication": {
            "topic_color": "#000080",  # 네이비 블루 (출판용)
            "keyword_color": "#8B0000",  # 다크 레드
            "edge_color": "#2F4F4F",  # 다크 슬레이트 그레이
            "background_color": "white",
            "node_size_factor": 0.85,  # 더 작은 노드
            "edge_width_factor": 0.7,  # 더 가는 선
            "layout_type": "grid",
            "font_size_topic": 32,  # 작은 폰트
            "font_size_keyword_min": 20,
            "font_size_keyword_max": 28,
            "adjust_text": True,  # 텍스트 위치 최적화
            "use_bbox": False,  # 텍스트 주변 박스 없음 - 더 깔끔한 스타일
        },
        "minimal": {
            "topic_color": "#000000",  # 검정 (최소한의 색상)
            "keyword_color": "#555555",  # 회색
            "edge_color": "#999999",  # 밝은 회색
            "background_color": "white",
            "node_size_factor": 0.8,  # 더 작은 노드
            "edge_width_factor": 0.6,  # 더 가는 선
            "layout_type": "grid",
            "font_size_topic": 30,  # 더 작은 폰트
            "font_size_keyword_min": 18,
            "font_size_keyword_max": 26,
            "adjust_text": True,  # 텍스트 위치 최적화
            "use_bbox": False,  # 텍스트 주변 박스 없음
            "high_contrast": True,  # 높은 대비
        },
        "circular": {
            "topic_color": "#1f77b4",
            "keyword_color": "#d62728",
            "edge_color": "#333333",
            "background_color": "white",
            "node_size_factor": 0.9,
            "edge_width_factor": 0.8,
            "layout_type": "circular",
            "font_size_topic": 32,
            "font_size_keyword_min": 24,
            "font_size_keyword_max": 30,
            "adjust_text": True,
            "use_bbox": True,
        },
    }

    # (7-3-6-2) 선택한 스타일이 없으면 기본값 사용
    if network_style not in style_configs:
        network_style = "academic"  # 논문용 스타일을 기본값으로

    # (7-3-6-3) 현재 스타일 구성 가져오기
    style = style_configs[network_style]

    # (7-3-7) 네트워크 시각화 초기화
    # (7-3-7-1) 더 큰 캔버스 사용
    plt.figure(figsize=(24, 24), facecolor=style["background_color"])

    # (7-3-7-2) 레이아웃 타입에 따른 위치 설정
    positions = {}

    # 문제 확인: 빈 데이터프레임인지 검사
    if topic_df.empty:
        print("경고: 토픽 데이터프레임이 비어 있습니다. 기본 네트워크를 생성합니다.")
        # 간단한 그래프 생성
        for i in range(num_topics):
            topic_name = f"Topic{i + 1}"
            G.add_node(topic_name)
            positions[topic_name] = (
                5 * np.cos(2 * np.pi * i / num_topics),
                5 * np.sin(2 * np.pi * i / num_topics),
            )
            G.nodes[topic_name]["color"] = style["topic_color"]
            G.nodes[topic_name]["size"] = 2000
            G.nodes[topic_name]["shape"] = "s"
            topic_nodes.append(topic_name)

    # (7-3-8) 레이아웃 유형별 노드 배치
    elif style["layout_type"] == "grid":
        # (7-3-8-1) 그리드 설정
        grid_size = int(np.ceil(np.sqrt(num_topics)))
        grid_positions = []

        # 그리드 좌표 생성
        canvas_size = 20
        margin = 4
        usable_size = canvas_size - 2 * margin
        step = usable_size / max(1, grid_size - 1) if grid_size > 1 else usable_size

        for i in range(grid_size):
            for j in range(grid_size):
                x = margin + i * step - canvas_size / 2
                y = margin + j * step - canvas_size / 2
                grid_positions.append((x, y))

        # 토픽 수만큼 그리드 위치 선택
        selected_positions = grid_positions[:num_topics]

        # (7-3-8-2) 토픽 노드 추가 및 배치
        for i, (grid_x, grid_y) in enumerate(selected_positions):
            topic_name = f"Topic{i + 1}"
            topic_nodes.append(topic_name)
            G.add_node(topic_name)
            topic_keyword_map[topic_name] = []

            # 토픽 노드 위치 설정
            positions[topic_name] = (grid_x, grid_y)

            # (7-3-8-3) 해당 토픽의 키워드 및 가중치 가져오기
            topic_keywords = topic_df[topic_df["topic"] == i]
            words = topic_keywords["word"].values
            weights = topic_keywords["weight"].values

            # 키워드 개수
            n_words = len(words)

            # (7-3-8-4) 키워드 배치 - 토픽 주변에 원형으로 배치하되 간격 넓게
            keyword_radius = 5.0  # 토픽에서 키워드까지 거리 증가

            for j, (word, weight) in enumerate(zip(words, weights)):
                topic_keyword_map[topic_name].append(word)

                if word not in keyword_nodes:
                    keyword_nodes.append(word)
                    keyword_weights[word] = weight

                    # 키워드를 토픽 주변에 원형으로 배치
                    angle = 2 * np.pi * j / n_words

                    # 가중치에 따른 거리 조정
                    norm_weight = (
                        (weight - min_weight) / weight_range
                        if weight_range > 0
                        else 0.5
                    )
                    weight_factor = 1.0 - min(0.3, norm_weight * 0.3)

                    kx = grid_x + keyword_radius * weight_factor * np.cos(angle)
                    ky = grid_y + keyword_radius * weight_factor * np.sin(angle)

                    # 최소한의 노이즈 추가 (토픽별 겹치지 않게 키워드 배치)
                    noise_scale = 0.2  # 노이즈 스케일 증가
                    kx += random.uniform(-noise_scale, noise_scale)
                    ky += random.uniform(-noise_scale, noise_scale)

                    positions[word] = (kx, ky)
                    G.add_node(word)

                # 토픽과 키워드 사이에 엣지 추가
                G.add_edge(topic_name, word, weight=weight)

    elif style["layout_type"] == "circular":
        # (7-3-8-5) 원형 레이아웃 설정
        # 토픽을 큰 원 주위에 배치
        topic_radius = 10.0
        for i in range(num_topics):
            topic_name = f"Topic{i + 1}"
            topic_nodes.append(topic_name)
            G.add_node(topic_name)
            topic_keyword_map[topic_name] = []

            # 토픽 노드를 원형으로 배치
            angle = 2 * np.pi * i / num_topics
            tx = topic_radius * np.cos(angle)
            ty = topic_radius * np.sin(angle)
            positions[topic_name] = (tx, ty)

            # 해당 토픽의 키워드 및 가중치 가져오기
            topic_keywords = topic_df[topic_df["topic"] == i]
            words = topic_keywords["word"].values
            weights = topic_keywords["weight"].values

            # 키워드 개수
            n_words = len(words)

            # (7-3-8-6) 키워드 배치 - 토픽 주변에 부채꼴 형태로 배치
            keyword_radius = 4.0
            angle_span = 2 * np.pi / num_topics  # 각 토픽이 차지하는 각도 공간
            angle_start = angle - angle_span / 2

            for j, (word, weight) in enumerate(zip(words, weights)):
                topic_keyword_map[topic_name].append(word)

                if word not in keyword_nodes:
                    keyword_nodes.append(word)
                    keyword_weights[word] = weight

                # 키워드를 토픽 주변 부채꼴에 배치
                word_angle = angle_start + (j + 1) * angle_span / (n_words + 1)

                # 가중치에 따른 거리 조정
                norm_weight = (
                    (weight - min_weight) / weight_range if weight_range > 0 else 0.5
                )
                weight_factor = 1.0 - min(0.3, norm_weight * 0.3)

                distance = topic_radius - keyword_radius * weight_factor
                kx = distance * np.cos(word_angle)
                ky = distance * np.sin(word_angle)

                # 최소한의 노이즈 추가
                noise_scale = 0.1
                kx += random.uniform(-noise_scale, noise_scale)
                ky += random.uniform(-noise_scale, noise_scale)

                positions[word] = (kx, ky)
                G.add_node(word)

            # 토픽과 키워드 사이에 엣지 추가
            G.add_edge(topic_name, word, weight=weight)

    # (7-3-9) 노드별 시각적 속성 설정
    for node in G.nodes():
        if node in topic_nodes:
            # (7-3-9-1) 토픽 노드: 스타일에 맞는 색상의 사각형
            G.nodes[node]["color"] = style["topic_color"]
            G.nodes[node]["size"] = 2200 * style["node_size_factor"]
            G.nodes[node]["shape"] = "s"
        else:
            # (7-3-9-2) 키워드 노드: 스타일에 맞는 색상의 원형
            weight = keyword_weights.get(node, 0)
            G.nodes[node]["color"] = style["keyword_color"]

            # 가중치 정규화
            norm_weight = (
                (weight - min_weight) / weight_range if weight_range > 0 else 0.5
            )

            # 크기 결정
            size_min = 400 * style["node_size_factor"]
            size_max = 2000 * style["node_size_factor"]  # 최대 크기 조정
            size_range = size_max - size_min

            log_weight = 0.1 + 0.9 * norm_weight
            G.nodes[node]["size"] = size_min + size_range * (log_weight**0.7)

    # (7-3-10) 엣지 그리기
    edge_weights = []
    for u, v in G.edges():
        weight = G[u][v].get("weight", 0)
        norm_edge_weight = (
            (weight - min_weight) / weight_range if weight_range > 0 else 0.5
        )
        edge_weights.append((0.8 + norm_edge_weight * 1.5) * style["edge_width_factor"])

    nx.draw_networkx_edges(
        G, positions, width=edge_weights, alpha=0.7, edge_color=style["edge_color"]
    )

    # (7-3-11) 키워드 노드 그리기
    for topic in topic_nodes:
        topic_keywords = topic_keyword_map[topic]
        if topic_keywords:
            nx.draw_networkx_nodes(
                G,
                positions,
                nodelist=topic_keywords,
                node_color=[G.nodes[n]["color"] for n in topic_keywords],
                node_size=[G.nodes[n]["size"] for n in topic_keywords],
                alpha=0.9,
                edgecolors="black",
                linewidths=1.0,
            )

    # (7-3-12) 토픽 노드 그리기
    nx.draw_networkx_nodes(
        G,
        positions,
        nodelist=topic_nodes,
        node_color=[G.nodes[n]["color"] for n in topic_nodes],
        node_size=[G.nodes[n]["size"] for n in topic_nodes],
        node_shape="s",
        edgecolors="black",
        linewidths=1.5,
    )

    # (7-3-13) 텍스트 레이블 설정
    # (7-3-13-1) 텍스트 색상 조정 (배경이 어두운 경우)
    text_color = (
        "white" if style["background_color"] in ["#222222", "black"] else "black"
    )

    # (7-3-13-2) 텍스트 레이블 저장 배열
    texts = []

    # (7-3-13-3) 토픽 레이블
    for node in topic_nodes:
        x, y = positions[node]

        # 텍스트 박스 옵션 설정
        bbox_props = None
        if style.get("use_bbox", True):
            bbox_props = dict(
                boxstyle="round,pad=0.3",
                fc="white" if text_color == "black" else "#444444",
                ec="black",
                alpha=0.95,
            )

        text = plt.text(
            x,
            y,
            node,
            fontsize=style["font_size_topic"],
            fontweight="bold",
            ha="center",
            va="center",
            color=text_color,
            bbox=bbox_props,
        )
        texts.append(text)

    # (7-3-13-4) 키워드 레이블
    for node in keyword_nodes:
        x, y = positions[node]
        weight = keyword_weights.get(node, 0)
        norm_weight = (weight - min_weight) / weight_range if weight_range > 0 else 0.5

        min_font = style["font_size_keyword_min"]
        max_font = style["font_size_keyword_max"]
        font_range = max_font - min_font
        fontsize = min_font + font_range * norm_weight

        # 텍스트 박스 옵션 설정
        bbox_props = None
        if style.get("use_bbox", True):
            bbox_props = dict(
                boxstyle="round,pad=0.2",
                fc="white" if text_color == "black" else "#444444",
                ec="lightgray" if text_color == "black" else "#666666",
                alpha=0.9,
            )

        text = plt.text(
            x,
            y,
            node,
            fontsize=fontsize,
            ha="center",
            va="center",
            color=text_color,
            bbox=bbox_props,
        )
        texts.append(text)

    # (7-3-14) 텍스트 위치 최적화 (겹침 방지)
    if style.get("adjust_text", False) and has_adjust_text:
        try:
            adjust_text(
                texts,
                x=[pos[0] for pos in positions.values()],
                y=[pos[1] for pos in positions.values()],
                force_points=0.5,
                force_text=0.5,
                expand_points=(1.5, 1.5),
                expand_text=(1.2, 1.2),
                arrowprops=dict(arrowstyle="-", color="gray", lw=0.5, alpha=0.4),
            )
        except Exception as e:
            print(f"텍스트 위치 최적화 오류: {e}")

    # (7-3-15) 그래프 제목 및 스타일 설정
    plt.title(
        "LDA Topic-Keyword Network Analysis", fontsize=36, pad=20, color=text_color
    )
    plt.axis("off")

    # (7-3-16) 여백 조정
    plt.tight_layout(pad=0.1)
    plt.subplots_adjust(left=0.01, right=0.99, top=0.97, bottom=0.05)

    # (7-3-17) 그래프 저장 및 경로 반환
    timestamp = int(random.random() * 10000)  # 캐시 방지용 타임스탬프 추가
    network_filename = f"topic_network_{network_style}_{timestamp}.png"
    network_img_path = os.path.join(topic_folder, network_filename)

    try:
        plt.savefig(
            network_img_path,
            dpi=300,
            bbox_inches="tight",
            facecolor=style["background_color"],
        )
        plt.close()
        print(f"네트워크 이미지 저장 성공: {network_img_path}")
    except Exception as e:
        print(f"네트워크 이미지 저장 오류: {e}")
        # 오류 발생 시 더 간단한 이미지로 시도
        plt.figure(figsize=(10, 10))
        plt.text(
            0.5, 0.5, "네트워크 시각화 생성 오류", ha="center", va="center", fontsize=20
        )
        plt.axis("off")
        try:
            network_filename = f"topic_network_error_{timestamp}.png"
            network_img_path = os.path.join(topic_folder, network_filename)
            plt.savefig(network_img_path, dpi=100)
            plt.close()
        except Exception as e2:
            print(f"백업 네트워크 이미지 저장 오류: {e2}")
            network_filename = "network_error.png"
            network_img_path = os.path.join(topic_folder, network_filename)
            # 기본 오류 이미지 생성 시도
            try:
                plt.figure(figsize=(8, 6))
                plt.text(
                    0.5,
                    0.5,
                    "네트워크 시각화 생성 오류",
                    ha="center",
                    va="center",
                    fontsize=20,
                )
                plt.axis("off")
                plt.savefig(network_img_path, dpi=100)
                plt.close()
            except Exception:
                pass

    return os.path.join(*network_img_path.split(os.sep)[-2:])


# (6-1-8-3) 각 토픽별 시각화를 PNG 파일로 저장
def create_topic_visualizations(final_model, used_topic_num, chart_style="default"):
    # 시각화 준비
    topic_images = []

    # 저장 폴더가 없으면 생성
    session_id = get_current_session_id()
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    analysis_folder = os.path.join(session_folder, "lda_analysis")
    topic_folder = os.path.join(
        analysis_folder, current_app.config["TOPIC_MODELS_FOLDER"]
    )

    try:
        os.makedirs(topic_folder, exist_ok=True)
        print("토픽 이미지 디렉토리 확인 완료")
    except Exception as e:
        print(f"디렉토리 생성 오류: {str(e)}")

    # 스타일 설정
    style_config = {
        "default": {
            "colors": ["#6495ED", "#F4A460", "#90EE90", "#F08080"],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.7,
            "background": "white",
            "figsize": (14, 9),
        },
        "colorful": {
            "colors": [
                "#FF6B6B",
                "#4ECDC4",
                "#FFA726",
                "#66BB6A",
                "#42A5F5",
                "#AB47BC",
            ],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.5,
            "background": "#F8F9FA",
            "figsize": (14, 9),
        },
        "minimal": {
            "colors": ["#555555", "#777777", "#999999", "#BBBBBB"],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.4,
            "background": "white",
            "figsize": (14, 9),
        },
        "dark": {
            "colors": ["#3498DB", "#E74C3C", "#2ECC71", "#F39C12", "#9B59B6"],
            "fontsize_title": 24,
            "fontsize_axes": 20,
            "fontsize_ticks": 16,
            "fontsize_values": 14,
            "grid_alpha": 0.6,
            "background": "#F8F9FA",
            "figsize": (14, 9),
        },
    }

    # 존재하지 않는 스타일이면 기본값 사용
    if chart_style not in style_config:
        chart_style = "default"

    # 현재 스타일 설정
    current_style = style_config[chart_style]
    topic_colors = current_style["colors"]

    for topic_id in range(used_topic_num):
        try:
            # 토픽의 상위 단어와 가중치 가져오기
            topic_words = final_model.show_topic(topic_id, topn=10)
            words = [word for word, _ in topic_words]
            weights = [weight for _, weight in topic_words]

            # 시각화를 위해 데이터 정렬 (가중치가 높은 순)
            indices = np.argsort(weights)[::-1]
            words = [words[i] for i in indices]
            weights = [weights[i] for i in indices]

            # 현재 토픽의 색상 선택
            color = topic_colors[topic_id % len(topic_colors)]

            # 토픽 시각화 - 스타일 설정 적용
            plt.figure(
                figsize=current_style["figsize"], facecolor=current_style["background"]
            )
            y_pos = np.arange(len(words))

            # 막대 그래프 생성
            bars = plt.barh(y_pos, weights, align="center", color=color)

            # 축 라벨 설정 - 글자 크기 증가
            plt.yticks(y_pos, words, fontsize=current_style["fontsize_ticks"])
            plt.xticks(fontsize=current_style["fontsize_ticks"])
            plt.xlabel(
                "가중치", fontsize=current_style["fontsize_axes"], fontweight="bold"
            )
            plt.ylabel(
                "키워드", fontsize=current_style["fontsize_axes"], fontweight="bold"
            )
            plt.title(
                f"TOPIC {topic_id + 1}",
                fontsize=current_style["fontsize_title"],
                fontweight="bold",
            )

            # 그리드 추가
            plt.grid(axis="x", linestyle="--", alpha=current_style["grid_alpha"])

            # 각 막대 끝에 값 표시 - 글자 크기 증가
            for i, v in enumerate(weights):
                plt.text(
                    v + 0.0005,
                    i,
                    f"{v:.3f}",
                    va="center",
                    fontsize=current_style["fontsize_values"],
                    fontweight="bold",
                )

            plt.tight_layout()

            # 이미지 저장 (캐시 방지를 위한 타임스탬프 추가)
            timestamp = int(random.random() * 10000)
            topic_img_path = os.path.join(
                topic_folder, f"topic_{topic_id + 1}_{timestamp}.png"
            )

            try:
                plt.savefig(topic_img_path, dpi=150, bbox_inches="tight")
                plt.close()

                # 상대 경로 생성 (세션 ID 포함)
                relative_path = "/".join([
                    current_app.config["TOPIC_MODELS_FOLDER"],
                    os.path.basename(topic_img_path),
                ])

                topic_images.append({"id": topic_id, "path": relative_path})
            except Exception as e:
                logger.error(f"토픽 {topic_id + 1} 이미지 저장 오류: {e}")
                plt.close()

                # 오류 발생 시 간단한 이미지 생성 시도
                try:
                    plt.figure(figsize=(10, 6))
                    plt.text(
                        0.5,
                        0.5,
                        f"토픽 {topic_id + 1} 시각화 오류",
                        ha="center",
                        va="center",
                        fontsize=20,
                    )
                    plt.axis("off")

                    err_topic_img_path = os.path.join(
                        topic_folder, f"topic_{topic_id + 1}_error_{timestamp}.png"
                    )
                    plt.savefig(err_topic_img_path, dpi=100)
                    plt.close()

                    relative_path = "/".join([
                        current_app.config["TOPIC_MODELS_FOLDER"],
                        os.path.basename(err_topic_img_path),
                    ])
                    topic_images.append({"id": topic_id, "path": relative_path})
                except Exception as e2:
                    logger.error(f"토픽 {topic_id + 1} 오류 이미지 생성 실패: {e2}")
                    topic_images.append(
                        {"id": topic_id, "path": f"topic_{topic_id + 1}_error.png"}
                    )
        except Exception as e:
            logger.error(f"토픽 {topic_id + 1} 처리 중 오류 발생: {e}")
            # 오류 발생 시에도 결과 목록에 추가
            timestamp = int(random.random() * 10000)
            try:
                plt.figure(figsize=(10, 6))
                plt.text(
                    0.5,
                    0.5,
                    f"토픽 {topic_id + 1} 처리 오류",
                    ha="center",
                    va="center",
                    fontsize=20,
                )
                plt.axis("off")

                err_topic_img_path = os.path.join(
                    topic_folder, f"topic_{topic_id + 1}_error_{timestamp}.png"
                )
                plt.savefig(err_topic_img_path, dpi=100)
                plt.close()

                relative_path = "/".join([
                    current_app.config["TOPIC_MODELS_FOLDER"],
                    os.path.basename(err_topic_img_path),
                ])
                topic_images.append({"id": topic_id, "path": relative_path})
            except Exception as e2:
                logger.error(f"토픽 {topic_id + 1} 오류 이미지 생성 실패: {e2}")
                topic_images.append(
                    {"id": topic_id, "path": f"topic_{topic_id + 1}_error.png"}
                )

    return topic_images


def save_lda_model(model, session_id, corpus=None, dictionary=None):
    """
    Save the trained LDA model and its associated data to pickle files in the session's model directory.

    Args:
        model: The trained LDA model
        session_id: Current session ID
        corpus: The document corpus used for training
        dictionary: The dictionary used for training
    """
    try:
        # Create session model directory if it doesn't exist
        model_dir = os.path.join(Config.MODEL_FOLDER, session_id)
        os.makedirs(model_dir, exist_ok=True)

        # Save the model
        model_path = os.path.join(model_dir, "lda_model.pkl")
        with open(model_path, "wb") as f:
            pickle.dump(model, f)

        # Save corpus if provided
        if corpus is not None:
            corpus_path = os.path.join(model_dir, "corpus.pkl")
            with open(corpus_path, "wb") as f:
                pickle.dump(corpus, f)

        # Save dictionary if provided
        if dictionary is not None:
            dict_path = os.path.join(model_dir, "dictionary.pkl")
            with open(dict_path, "wb") as f:
                pickle.dump(dictionary, f)

        return True
    except Exception as e:
        logger.error(f"Error saving LDA model and data: {str(e)}")
        return False


def load_lda_model(session_id):
    """
    Load a trained LDA model and its associated data from the session's model directory.

    Args:
        session_id: Session ID to load the model from

    Returns:
        tuple: (model, corpus, dictionary) or (None, None, None) if not found/error
    """
    try:
        model_dir = os.path.join(Config.MODEL_FOLDER, session_id)
        model_path = os.path.join(model_dir, "lda_model.pkl")
        corpus_path = os.path.join(model_dir, "corpus.pkl")
        dict_path = os.path.join(model_dir, "dictionary.pkl")

        # Check if all required files exist
        if not all(os.path.exists(p) for p in [model_path, corpus_path, dict_path]):
            return None, None, None

        # Load model
        with open(model_path, "rb") as f:
            model = pickle.load(f)

        # Load corpus
        with open(corpus_path, "rb") as f:
            corpus = pickle.load(f)

        # Load dictionary
        with open(dict_path, "rb") as f:
            dictionary = pickle.load(f)

        return model, corpus, dictionary
    except Exception as e:
        logger.error(f"Error loading LDA model and data: {str(e)}")
        return None, None, None


# =========================TF-IDF===================================


class TFIDFAnalyzer:
    """TF-IDF analysis service"""

    def __init__(self, analysis_type="tfidf"):
        self.result_folder = current_app.config["RESULT_FOLDER"]
        self.analysis_type = analysis_type

    def analyze_tfidf(
        self,
        file_path,
        column_name,
        session_id,
        selection_type="top_n",
        max_words=50,
        cloud_shape="rectangle",
        cloud_color="viridis",
        selected_words=None,
        replaced_words=None,
        excluded_words=None,
    ):
        """
        Perform TF-IDF analysis on text data

        Args:
            file_path (str): Path to the input file
            column_name (str): Name of the column to analyze
            session_id (str): Session identifier
            selection_type (str): 'top_n' or 'manual'
            max_words (str): Maximum number of words to include
            cloud_shape (str): Shape for wordcloud ('rectangle' or 'circle')
            cloud_color (str): Color theme for wordcloud
            selected_words (str): JSON string of selected words (for manual mode)
            replaced_words (str): JSON string of replaced words
            excluded_words (str): JSON string of excluded words

        Returns:
            dict: Analysis results
        """
        try:
            # Read file

            data = read_file(file_path)

            # Check if column exists
            if column_name not in data.columns:
                return {"error": f"'{column_name}' 컬럼을 찾을 수 없습니다."}

            # Generate unique filename
            filename_base = f"tfidf_{session_id}_{uuid.uuid4().hex[:8]}"

            # Process text data
            data[column_name] = data[column_name].fillna("").astype(str)

            # Apply word replacements
            if replaced_words:
                for word_info in replaced_words:
                    original_word = word_info.get("original")
                    new_word = word_info.get("new")
                    if original_word and new_word:
                        data[column_name] = data[column_name].str.replace(
                            original_word, new_word, regex=False
                        )
            # Exclude words
            if excluded_words:
                for word in excluded_words:
                    data[column_name] = data[column_name].str.replace(
                        word, "", regex=False
                    )

            # Extract TF (Term Frequency)
            cv = CountVectorizer(max_features=300, ngram_range=(1, 1))
            tdm = cv.fit_transform(data[column_name])

            # Apply TF-IDF transformation
            trans = TfidfTransformer()
            dtm2 = trans.fit_transform(tdm)

            # Convert to DataFrame
            df2 = pd.DataFrame(
                {"단어": cv.get_feature_names_out(), "tf-idf": dtm2.sum(axis=0).flat}
            )

            # Sort by TF-IDF values (descending)
            df2 = df2.sort_values("tf-idf", ascending=False)
            df2 = df2.reset_index(drop=True)

            # Calculate percentages
            total_tfidf = df2["tf-idf"].sum()
            df2["비율(%)"] = (df2["tf-idf"] / total_tfidf) * 100

            # Save results to CSV
            csv_filename = f"{filename_base}_tf_idf.csv"
            csv_path = os.path.join(self.result_folder, csv_filename)
            df2.to_csv(csv_path, encoding="cp949", index=False)

            # Prepare top words data (top 200)
            top_words = (
                df2.head(200)
                .apply(
                    lambda row: {"word": row["단어"], "tfidf": float(row["tf-idf"])},
                    axis=1,
                )
                .tolist()
            )

            # Return word data only for manual selection mode
            if selection_type == "manual" and selected_words is None:
                return {
                    "success": True,
                    "output_file": csv_filename,
                    "word_data": top_words,
                }

            # Generate wordcloud
            wordcloud_result = self._generate_wordcloud(
                df2,
                filename_base,
                selection_type,
                max_words,
                cloud_shape,
                cloud_color,
                selected_words,
            )

            result = {
                "success": True,
                "output_file": csv_filename,
                "word_data": top_words,
            }

            if wordcloud_result.get("success"):
                result["wordcloud_file"] = wordcloud_result["wordcloud_file"]
                result["file_path"] = wordcloud_result["file_path"]

            return result

        except Exception as e:
            logger.error(f"TF-IDF 분석 오류: {str(e)}")
            return {"error": str(e)}

    def _generate_wordcloud(
        self,
        df,
        filename_base,
        selection_type,
        max_words,
        cloud_shape,
        cloud_color,
        selected_words,
    ):
        """Generate wordcloud from TF-IDF results"""
        try:
            import json

            # Prepare word dictionary for wordcloud
            if selection_type == "manual" and selected_words:
                # Manual selection mode
                word_list = json.loads(selected_words)
                count_dict = {word: 1 for word in word_list}
                # count_dict
            else:
                # Automatic selection mode
                top_df = df.head(int(max_words))
                count_dict = dict(zip(top_df["단어"], top_df["tf-idf"]))

            # Generate wordcloud
            wordcloud_generator = WordCloudGenerator(
                analysis_type="tfidf"
            )  # Add analysis_type argument
            return wordcloud_generator.generate_wordcloud(
                count_dict, filename_base, cloud_shape, cloud_color
            )

        except Exception as e:
            logger.error(f"워드클라우드 생성 오류: {str(e)}")
            return {"error": str(e)}


class WordCloudGenerator:
    """WordCloud generation service"""

    def __init__(self, analysis_type="tfidf"):
        self.result_folder = current_app.config["RESULT_FOLDER"]
        self.font_path = current_app.config["FONT_PATH"]
        self.analysis_type = analysis_type

    def create_wordcloud_mask(self, shape, width=1000, height=1000):
        """
        Create mask for wordcloud shape

        Args:
            shape (str): Shape type ('circle' or 'rectangle')
            width (int): Mask width
            height (int): Mask height

        Returns:
            numpy.ndarray or None: Mask array or None for rectangle
        """
        if shape == "circle":
            x, y = np.ogrid[:width, :height]
            center = width / 2, height / 2
            radius = min(width, height) / 2 * 0.9  # 90% of the minimum dimension

            # Select area outside circle (True)
            circle_mask = (x - center[0]) ** 2 + (y - center[1]) ** 2 > radius**2

            # Invert mask: WordCloud draws words in areas where value is 0
            # So set inside circle to 0, outside to 255
            return 255 * circle_mask.astype(int)
        return None

    def get_color_function(self, color_theme):
        """
        Generate color function for wordcloud

        Args:
            color_theme (str): Color theme name (matplotlib colormap)

        Returns:
            function: Color function for WordCloud
        """
        colormap = plt.cm.get_cmap(color_theme)

        def color_func(*args, **kwargs):
            return tuple(int(x * 255) for x in colormap(random.random()))

        return color_func

    def generate_wordcloud(
        self, count_dict, filename_base, cloud_shape="rectangle", cloud_color="viridis"
    ):
        """
        Generate wordcloud from word dictionary

        Args:
            count_dict (dict): Dictionary of word: frequency pairs
            filename_base (str): Base filename for output
            cloud_shape (str): Shape for wordcloud
            cloud_color (str): Color theme for wordcloud

        Returns:
            dict: Generation result with success status and filename
        """
        try:
            wordcloud_filename = f"{filename_base}_wordcloud.png"
            session_id = get_current_session_id()
            analysis_folder = os.path.join(
                self.result_folder,
                session_id,
                current_app.config["WORD_CLOUD_FOLDER"],
                self.analysis_type,
            )
            wordcloud_path = os.path.join(analysis_folder, wordcloud_filename)

            # Create analysis folder if it doesn't exist
            os.makedirs(
                analysis_folder, exist_ok=True
            )  # Add this line to create the folder if it doesn't exist

            # Create mask and color function
            mask = self.create_wordcloud_mask(cloud_shape)
            color_func = self.get_color_function(cloud_color)

            # Generate WordCloud
            wc = WordCloud(
                background_color="white",
                max_words=len(count_dict),
                width=1000,
                height=1000,
                font_path=self.font_path,
                mask=mask,
                color_func=color_func,
            )

            cloud = wc.fit_words(count_dict)
            cloud.to_file(wordcloud_path)
            file_sub_path = "/".join([
                current_app.config["WORD_CLOUD_FOLDER"],
                self.analysis_type, wordcloud_filename])
            return {"success": True, "wordcloud_file": wordcloud_filename, "file_path": file_sub_path}

        except Exception as e:
            logger.error(f"워드클라우드 생성 오류: {str(e)}")
            return {"error": str(e)}

    def generate_from_words(
        self,
        word_list,
        session_id,
        cloud_shape="rectangle",
        cloud_color="viridis",
        prefix="wordcloud",
    ):
        """
        Generate wordcloud from list of words

        Args:
            word_list (list): List of words
            session_id (str): Session identifier
            cloud_shape (str): Shape for wordcloud
            cloud_color (str): Color theme for wordcloud
            prefix (str): Filename prefix

        Returns:
            dict: Generation result with success status and filename
        """
        try:
            # Create filename
            filename_base = (
                f"{self.analysis_type}_{session_id}_{prefix}_{uuid.uuid4().hex[:8]}"
            )

            # Create word dictionary (equal frequency for all words)
            count_dict = {word: 1 for word in word_list}

            return self.generate_wordcloud(
                count_dict, filename_base, cloud_shape, cloud_color
            )

        except Exception as e:
            logger.error(f"단어 리스트로부터 워드클라우드 생성 오류: {str(e)}")
            return {"error": str(e)}


# ======================================NGRAM==========================================




class NGramAnalyzer:
    """N-gram analysis service"""

    def __init__(self):
        self.result_folder = current_app.config["RESULT_FOLDER"]
        self.ngram_folder = current_app.config.get("NGRAM_FOLDER", "ngram")

    def _generate_filename_base(self, n_gram_value):
        """Generate base filename for results"""
        return f"ngram_{uuid.uuid4().hex[:8]}_{n_gram_value}gram"

    def _prepare_data(self, data, column_name):
        """Prepare text data for analysis"""
        if column_name not in data.columns:
            raise ValueError(f"'{column_name}' 컬럼을 찾을 수 없습니다.")

        # Handle non-string values (convert NaN to empty string and all values to string)
        data[column_name] = data[column_name].fillna("").astype(str)
        return data[column_name]

    def _extract_ngrams(self, text_data, n_gram_value, max_features):
        """Extract n-grams from text data"""
        cv = CountVectorizer(
            max_features=int(max_features), ngram_range=(n_gram_value, n_gram_value)
        )
        tdm = cv.fit_transform(text_data)

        # Convert results to DataFrame
        word_count = pd.DataFrame(
            {"단어": cv.get_feature_names_out(), "빈도": tdm.sum(axis=0).flat}
        )

        # Sort by frequency in descending order
        word_count = word_count.sort_values("빈도", ascending=False)
        word_count = word_count.reset_index(drop=True)

        return word_count

    def _save_excel_results(self, word_count, filename_base):
        """Save analysis results to Excel file"""
        # from ..services.session_service import get_or_create_session_id

        excel_filename = f"{filename_base}_result.xlsx"
        session_id = get_current_session_id()
        analysis_folder = os.path.join(
            self.result_folder,
            session_id,
            current_app.config.get("NGRAM_FOLDER", "ngram"),
        )

        # Create analysis folder if it doesn't exist
        os.makedirs(analysis_folder, exist_ok=True)

        excel_path = os.path.join(analysis_folder, excel_filename)
        word_count.to_excel(excel_path, index=True)

        return excel_filename

    def _prepare_word_dict_for_wordcloud(
        self, word_count, selection_type, max_words, selected_words
    ):
        """Prepare word dictionary for wordcloud generation"""
        if selection_type == "manual" and selected_words:
            # Manual selection mode: use only selected words
            word_list = json.loads(selected_words)
            # Get frequencies for selected words
            word_freq_dict = dict(zip(word_count["단어"], word_count["빈도"]))
            count_dict = {word: word_freq_dict.get(word, 1) for word in word_list}
        else:
            # Auto selection mode: use top N words
            top_words_df = word_count.head(int(max_words))
            count_dict = dict(zip(top_words_df["단어"], top_words_df["빈도"]))

        return count_dict

    def _generate_default_filename(self, original_filename, n_gram_value):
        """Generate default download filename"""
        current_datetime = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        base_name = (
            original_filename.split(".")[0]
            if "." in original_filename
            else original_filename
        )
        return f"{current_datetime}_{base_name}_{n_gram_value}gram"

    def analyze(
        self,
        file_path,
        column_name,
        n_gram,
        max_features,
        selection_type=None,
        max_words=None,
        cloud_shape=None,
        cloud_color=None,
        selected_words=None,
    ):
        """
        Perform n-gram analysis and optionally generate word cloud

        Args:
            file_path (str): Path to the input file
            column_name (str): Name of the column to analyze
            n_gram (int): N-gram size
            max_features (int): Maximum number of features to extract
            selection_type (str): Selection type for wordcloud ('manual' or 'auto')
            max_words (int): Maximum words for wordcloud (auto mode)
            cloud_shape (str): Shape for wordcloud
            cloud_color (str): Color theme for wordcloud
            selected_words (str): JSON string of selected words (manual mode)

        Returns:
            dict: Analysis results
        """
        try:
            # Extract original filename
            original_filename = os.path.basename(file_path)
            current_datetime = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")

            # Read file
            data = read_file(file_path)

            # Prepare data
            text_data = self._prepare_data(data, column_name)

            # Set n-gram value
            n_gram_value = int(n_gram)

            # Generate filename base
            filename_base = self._generate_filename_base(n_gram_value)

            # Extract n-grams
            word_count = self._extract_ngrams(text_data, n_gram_value, max_features)

            # Save Excel results
            excel_filename = self._save_excel_results(word_count, filename_base)

            # Generate default filename for download
            default_filename = self._generate_default_filename(
                original_filename, n_gram_value
            )

            # Base result
            result = {
                "success": True,
                "output_file": excel_filename,
                "top_words": word_count.head(300).to_dict("records"),
                "original_filename": original_filename,
                "analysis_datetime": current_datetime,
                "default_filename": default_filename,
                "n_gram": n_gram_value,
            }

            # If no wordcloud generation requested, return basic results
            if selection_type is None:
                return result

            # Manual selection mode: return word data for selection
            if selection_type == "manual" and selected_words is None:
                top_words = (
                    word_count.head(300)
                    .apply(
                        lambda row: {
                            "word": row["단어"],
                            "frequency": int(row["빈도"]),
                        },
                        axis=1,
                    )
                    .tolist()
                )
                result["word_data"] = top_words
                return result

            # Generate wordcloud
            count_dict = self._prepare_word_dict_for_wordcloud(
                word_count, selection_type, max_words, selected_words
            )

            # Use WordCloudGenerator to create wordcloud
            wordcloud_generator = WordCloudGenerator(analysis_type="ngram")
            wordcloud_result = wordcloud_generator.generate_wordcloud(
                count_dict=count_dict,
                filename_base=filename_base,
                cloud_shape=cloud_shape or "rectangle",
                cloud_color=cloud_color or "viridis",
            )
            # Add wordcloud result to main result
            if wordcloud_result.get("success"):
                result["wordcloud_file"] = wordcloud_result["wordcloud_file"]
                logger.info(
                    f"워드클라우드 생성 성공: {wordcloud_result['wordcloud_file']}"
                )
            else:
                logger.error(
                    f"워드 클라우드 생성 오류: {wordcloud_result.get('error', 'Unknown error')}"
                )
                result["wordcloud_file"] = None

            return result

        except Exception as e:
            logger.error(f"n-gram 분석 오류: {traceback.format_exc()}")
            return {"error": str(e)}

# ===========================================BERT================================



def analyze_topics(df, column_name, params=None):
    if params is None:
        params = {}
    
    try:
        # 데이터 전처리 최적화
        df = df.dropna(subset=[column_name])
        df = df[df[column_name].astype(str).str.strip() != ""]
        
        # 데이터 크기 제한 (너무 큰 데이터셋은 샘플링)
        max_docs = 2000  # 최대 2000개 문서로 제한
        if len(df) > max_docs:
            print(f"데이터가 너무 많습니다. {max_docs}개 샘플만 사용합니다.")
            df = df.sample(max_docs, random_state=42)
        
        preprocessed_documents = df[column_name].astype(str).values.tolist()
        print(f"분석할 데이터 크기: {len(preprocessed_documents)}개 문서")
        
        if len(preprocessed_documents) < 10:
            raise ValueError("분석할 문서가 너무 적습니다. 최소 10개 이상의 문서가 필요합니다.")
        
        # 임베딩 모델 설정 - 더 빠른 임베딩 모델 기본값으로
        embedding_model_name = params.get('embedding_model', 
                                      "sentence-transformers/paraphrase-multilingual-mpnet-base-v2")
        
        # 임베딩 모델 초기화 부분 주변에 예외 처리 추가
        try:
            print(f"임베딩 모델 로딩 중: {embedding_model_name}")
            embedding_model = SentenceTransformer(embedding_model_name)
        except Exception as model_err:
            print(f"모델 로딩 중 오류: {model_err}, 기본 모델로 대체합니다.")
            # 오류 발생 시 안정적인 기본 모델로 폴백
            embedding_model = SentenceTransformer("sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")
        
        # CountVectorizer 설정 - 성능 영향 최소화
        vectorizer = CountVectorizer(
            ngram_range=(1, min(int(params.get('max_ngram', 1)), 3)),
            min_df=int(params.get('min_df', 2))
        )
        
        # BERTopic 모델 설정 - 성능 최적화 옵션
        nr_topics = params.get('nr_topics', "auto")
        if nr_topics != "auto":
            nr_topics = int(nr_topics)
        else:
            nr_topics = min(15, max(5, len(preprocessed_documents) // 20))
            print(f"자동 토픽 수 결정: {nr_topics}개")
        
        # 모델 생성 - 불필요한 연산 제거
        model = BERTopic(
            embedding_model=embedding_model,
            vectorizer_model=vectorizer,
            nr_topics=nr_topics,
            top_n_words=int(params.get('top_n_words', 10)),
            min_topic_size=int(params.get('min_topic_size', 5)),
            verbose=True,
            calculate_probabilities=False,  # 속도 향상
            low_memory=True  # 메모리 사용 최적화
        )
        
        # 세션 ID 생성
        
        result_folder = current_app.config["RESULT_FOLDER"]
        session_id = get_current_session_id()
        session_folder = os.path.join(result_folder, session_id)
        analysis_folder = os.path.join(session_folder, "bertopic_analysis")
        # 토픽 모델링 수행
        print("토픽 모델링 시작...")
        topics, _ = model.fit_transform(preprocessed_documents)
        print("토픽 모델링 완료")
        
        # 토픽 정보 추출
        topic_info = model.get_topic_info()
        
        # CSV 결과 저장
        topic_info.to_csv(os.path.join(analysis_folder, 'topic_info.csv'), index=False)
        
        # 시각화 추가
        try:
            # 1. 토픽 분포 시각화
            fig_dist = model.visualize_topics()
            plotly.io.write_html(fig_dist, os.path.join(analysis_folder, 'topic_distribution.html'))
            
            # 2. 토픽 계층 구조 시각화
            try:
                fig_hier = model.visualize_hierarchy()
                plotly.io.write_html(fig_hier, os.path.join(analysis_folder, 'topic_hierarchy.html'))
            except Exception as viz_err:
                print(f"계층 시각화 생성 오류 (무시함): {viz_err}")
            
            # 3. 토픽 단어 막대 그래프
            fig_words = model.visualize_barchart(top_n_topics=10)
            plotly.io.write_html(fig_words, os.path.join(analysis_folder, 'topic_words.html'))
            
            # 4. 히트맵 (간단한 토픽 간 관계)
            try:
                fig_heatmap = model.visualize_heatmap()
                plotly.io.write_html(fig_heatmap, os.path.join(analysis_folder, 'topic_heatmap.html'))
            except Exception as viz_err:
                print(f"히트맵 시각화 생성 오류 (무시함): {viz_err}")
            
            # 시각화 완료 플래그
            visualization_success = True
        except Exception as viz_err:
            print(f"시각화 생성 중 오류 발생 (분석은 계속 진행): {viz_err}")
            visualization_success = False
        
        # 유효한 토픽만 필터링
        valid_topics = topic_info[topic_info['Topic'] != -1]
        
        if len(valid_topics) == 0:
            raise ValueError("유효한 토픽을 찾을 수 없습니다. 다른 텍스트 열이나 설정을 시도해보세요.")
        
        # 토픽별 키워드와 대표 문서 추출 (최적화)
        representative_docs = {}
        topic_keywords = {}
        
        print("토픽 세부 정보 추출 중...")
        for topic_id in valid_topics['Topic'].values:
            topic_id = int(topic_id)
            
            # 토픽 키워드 추출
            keywords = model.get_topic(topic_id)
            topic_keywords[topic_id] = keywords
            
            # 대표 문서 최대 5개만 추출
            try:
                docs = model.get_representative_docs(topic_id)
                representative_docs[topic_id] = docs[:5] if docs else []
            except Exception as e:
                print(f"토픽 {topic_id} 대표 문서 추출 실패: {e}")
                representative_docs[topic_id] = []
        
        # 토픽 요약 정보 생성
        topic_summary = []
        for _, row in valid_topics.iterrows():
            topic_id = int(row['Topic'])
            count = row['Count']
            keywords = [word for word, _ in topic_keywords.get(topic_id, [])][:7]
            topic_summary.append({
                'topic_id': topic_id,
                'size': count,
                'keywords': ', '.join(keywords)
            })
        
        # 문서-토픽 매핑 정보 (최적화: 최대 1000개만 반환)
        document_topics = []
        max_docs_to_return = min(1000, len(preprocessed_documents))
        
        for i, (doc, topic) in enumerate(zip(preprocessed_documents[:max_docs_to_return], topics[:max_docs_to_return])):
            if len(doc) > 100:
                doc = doc[:100] + "..."
            document_topics.append({
                'id': i,
                'topic': int(topic),
                'document': doc
            })
        
        # 토픽 단어 점수 (최적화)
        topic_word_scores = {}
        for topic_id in valid_topics['Topic'].values:
            topic_id = int(topic_id)
            words_with_scores = model.get_topic(topic_id)
            if words_with_scores:
                topic_word_scores[topic_id] = [
                    {"word": word, "score": round(score, 3)} 
                    for word, score in words_with_scores[:10]
                ]
        
        # 결과 JSON 저장 - 필수 데이터만 저장
        analysis_results = {
            'topic_summary': topic_summary,
            'topic_keywords': {k: [w[0] for w in v] for k, v in topic_keywords.items()}
        }
        
        with open(os.path.join(analysis_folder, 'analysis_results.json'), 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2)
        
        # 토픽별 문서 분포 CSV 생성
        topic_counts = pd.Series(topics).value_counts().sort_index()
        topic_counts = topic_counts[topic_counts.index != -1]  # -1 토픽 제외
        topic_dist_df = pd.DataFrame({
            '토픽 ID': topic_counts.index,
            '문서 수': topic_counts.values
        })
        topic_dist_df.to_csv(os.path.join(analysis_folder, 'topic_distribution.csv'), index=False)
        
        # Excel 내보내기 추가 - 상세 분석 결과
        try:
            # 토픽별 문서 Excel 파일 생성
            topic_docs_df = pd.DataFrame({
                'Document_ID': range(len(preprocessed_documents[:max_docs_to_return])),
                'Document': preprocessed_documents[:max_docs_to_return],
                'Topic': topics[:max_docs_to_return]
            })
            
            # 토픽별 키워드를 DataFrame으로 변환
            keywords_data = []
            for topic_id in valid_topics['Topic'].values:
                topic_id = int(topic_id)
                keywords = topic_keywords.get(topic_id, [])
                for i, (word, score) in enumerate(keywords[:10]):
                    keywords_data.append({
                        'Topic': topic_id,
                        'Rank': i+1,
                        'Keyword': word,
                        'Score': score
                    })
            keywords_df = pd.DataFrame(keywords_data)
            
            # Excel 파일로 저장
            with pd.ExcelWriter(os.path.join(analysis_folder, 'topic_analysis_results.xlsx')) as writer:
                topic_info.to_excel(writer, sheet_name='Topic_Info', index=False)
                topic_docs_df.to_excel(writer, sheet_name='Documents', index=False)
                keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
        except Exception as excel_err:
            print(f"Excel 파일 생성 중 오류 발생 (무시함): {excel_err}")
        
        print("분석 완료")
        # 시각화 관련 키 제거하고 필요한 데이터만 반환
        return {
            'topic_info': valid_topics.to_dict('records'),
            'representative_docs': representative_docs,
            'topic_keywords': topic_keywords,
            'topic_summary': topic_summary,
            'document_topics': document_topics,
            'topic_word_scores': topic_word_scores,
            'total_docs': len(preprocessed_documents),
            'returned_docs': max_docs_to_return,
            'session_id': session_id,
            'visualization_available': visualization_success
        }
    
    except ValueError as ve:
        print(f"분석 중 값 오류 발생: {str(ve)}")
        raise
    except Exception as e:
        print(f"분석 중 오류 발생: {str(e)}")
        print(traceback.format_exc())
        raise