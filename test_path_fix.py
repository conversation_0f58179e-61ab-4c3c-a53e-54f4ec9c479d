#!/usr/bin/env python3
"""
Test script to verify that the path separator fix works correctly.
"""

import os
import sys

# Add backend to path
sys.path.append('backend')

def test_path_generation():
    """Test that paths are generated with forward slashes for URLs"""
    
    # Simulate the old way (problematic)
    old_way = os.path.join("word_clouds", "ngram", "test_file.png")
    print(f"Old way (os.path.join): {old_way}")
    print(f"Contains backslashes: {'\\' in old_way}")
    
    # Simulate the new way (fixed)
    new_way = "/".join(["word_clouds", "ngram", "test_file.png"])
    print(f"New way (/join): {new_way}")
    print(f"Contains backslashes: {'\\' in new_way}")
    
    print("\n" + "="*50)
    print("URL Path Test Results:")
    print("="*50)
    
    if "\\" in old_way:
        print("❌ Old method produces backslashes (problematic for URLs)")
    else:
        print("✅ Old method produces forward slashes")
        
    if "\\" not in new_way:
        print("✅ New method produces forward slashes (correct for URLs)")
    else:
        print("❌ New method produces backslashes")

if __name__ == "__main__":
    test_path_generation()
