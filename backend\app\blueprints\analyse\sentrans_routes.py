
from flask import Flask, render_template, request, jsonify, send_file, current_app
import os
import json
import matplotlib
# backend 설정 - Tkinter 대신 Agg 사용 (서버 환경에 적합)
matplotlib.use('Agg')
from ...services.senttrans import analyze_word_similarity, AVAILABLE_MODELS
from ...services.nlp_service import get_file_path
from . import sentrans_bp
from ...services.session_service import get_or_create_session_id, get_session_value

@sentrans_bp.route('/analyze', methods=['POST'])
def analyze():
    """파일 분석 API"""
    try:

        # 필수 파라미터 확인
        column_name = request.form.get('column_name')
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        # 임시 파일로 저장
        temp_file = None
        try:
            session_id = get_or_create_session_id()            
            # temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1], dir=app.config['TEMP_FOLDER'])
            # file.save(temp_file.name)
            # temp_file.close()
            filename = get_session_value(session_id, "uploaded_file")
            file_path = get_file_path(session_id, filename)
            

            # 추가 파라미터 (옵션)
            # First, get the selection type as it's needed within the options dict itself
            selection_type = request.form.get('selection_type', 'auto')

            options = {
                'max_features': request.form.get('max_features', '3000'),
                'top_n_similar': request.form.get('top_n_similar', '10'),
                'selection_type': selection_type, # Use the pre-fetched value
                'visualization_shape': request.form.get('visualization_shape', 'circular'),
                'visualization_color': request.form.get('visualization_color', 'pastel'),
                'model_id': request.form.get('model_id', 'jhgan/ko-sbert-sts'),
                'target_ngram': request.form.get('target_ngram', '1'),
                 # Use the pre-fetched selection_type to conditionally get specific_word
                'specific_word': request.form.get('specific_word') if selection_type == 'specific_input' else None
            }

            # Add pairwise specific words if applicable (using pre-fetched selection_type)
            if selection_type == 'pairwise_specific_input':
                options['specific_word_a'] = request.form.get('specific_word_a')
                options['specific_word_b'] = request.form.get('specific_word_b')


            # 수동 선택 모드인 경우 선택된 단어 추가
            # Use the pre-fetched selection_type here as well
            if selection_type == 'manual' and 'selected_words' in request.form:
                try:
                    options['selected_words'] = json.loads(request.form.get('selected_words', '[]'))
                except Exception as e:
                    return jsonify({"error": "선택된 단어 목록을 처리할 수 없습니다"}), 400

            # Log specific word if provided (using the pre-fetched selection_type)
            if selection_type == 'specific_input' and options.get('specific_word'):
                print(options['specific_word'])
            elif selection_type == 'specific_input' and not options.get('specific_word'):
                 # This check might be redundant now but kept for safety
                print("선택된 단어가 없습니다.")
                 # The main check is now inside analyze_word_similarity
            # Add check for pairwise specific words
            elif selection_type == 'pairwise_specific_input':
                word_a = options.get('specific_word_a')
                word_b = options.get('specific_word_b')
                # if word_a and word_b:
                #     print(word_a, word_b)            
                #     # Error will be handled in analyze_word_similarity

            # 분석 실행
            result = analyze_word_similarity(file_path, column_name, options)

            if "error" in result:
                return jsonify(result), 400

            return jsonify(result)
        finally:
            # 임시 파일 삭제
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@sentrans_bp.route('/image/<path:filename>')
def get_image(filename):
    """이미지 파일 조회 API"""
    try:
        return send_file(os.path.join(current_app.config['IMAGE_FOLDER'], filename))
    except Exception as e:
        
        return jsonify({"error": f"이미지를 찾을 수 없습니다: {str(e)}"}), 500

@sentrans_bp.route('/get_models', methods=['GET'])
def get_models():
    """사용 가능한 임베딩 모델 목록 반환"""
    return jsonify({"models": AVAILABLE_MODELS})

