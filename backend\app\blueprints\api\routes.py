import json
import os
import shutil

from flask import Flask, current_app, jsonify, request, send_from_directory
from flask_cors import CORS

from ...services.json_session_service import delete_session, read_session_data
from ...services.session_service import (get_or_create_session_id,
                                         get_session_by_id, get_session_status)
from . import api

CORS(api)


# Configure CORS for React frontend with HTTPS support
@api.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    return response


@api.route("/swaggerui")
def swagger_json():
    # Load your static swagger.json file
    print("CURRENT: CWD", os.getcwd())
    with open("./app/static/swagger.json") as f:
        spec = json.load(f)

    # Dynamically update `host` and `schemes`
    spec["host"] = request.host
    print("THIS IS THE REQUEST HOST", request.host)
    spec["schemes"] = [request.scheme]

    response = jsonify(spec)
    # Add CORS headers explicitly for Swagger UI
    response.headers.add("Access-Control-Allow-Origin", "*")
    response.headers.add("Access-Control-Allow-Headers", "Content-Type,Authorization")
    response.headers.add("Access-Control-Allow-Methods", "GET,OPTIONS")

    return response


@api.route("/session-status", methods=["GET"])
def session_status():
    """
    Get the current session status.
    If no session exists, a new one will be created.

    Returns:
        JSON with session status information
    """
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    if query_session_id:
        # Get session data for the specified ID
        session_data = get_session_by_id(query_session_id)

        # If session found, return it
        if session_data:
            return jsonify(
                {
                    "active": True,
                    "session_id": query_session_id,
                    "data": session_data,
                    "message": "Using provided session ID",
                }
            )
        else:
            # If session not found, return 404
            return (
                jsonify(
                    {
                        "active": False,
                        "message": f"Session with ID {query_session_id} not found",
                    }
                ),
                404,
            )

    # Get session status
    status = get_session_status()

    # If no active session, create one
    if not status["active"]:
        session_id = get_or_create_session_id()
        status = {
            "active": True,
            "session_id": session_id,
            "uploaded_file": None,
            "has_file": False,
            "message": "New session created",
        }

    return jsonify(status)


@api.route("/session-debug", methods=["GET"])
def session_debug():
    """
    Debug endpoint to check session configuration and status.
    Provides detailed information about the current session.
    """
    # Get session ID from request
    session_id = request.args.get("session_id")
    if not session_id:
        session_id = get_or_create_session_id()

    # Get session data from JSON file
    session_data = read_session_data(session_id) or {}

    # Get Flask app configuration related to sessions
    session_config = {
        "SESSION_TYPE": current_app.config.get("SESSION_TYPE"),
        "SESSION_FILE_DIR": current_app.config.get("SESSION_FILE_DIR"),
        "SESSION_PERMANENT": current_app.config.get("SESSION_PERMANENT"),
        "PERMANENT_SESSION_LIFETIME": str(
            current_app.config.get("PERMANENT_SESSION_LIFETIME")
        ),
        "SESSION_COOKIE_NAME": current_app.config.get("SESSION_COOKIE_NAME"),
        "SESSION_COOKIE_SECURE": current_app.config.get("SESSION_COOKIE_SECURE"),
        "SESSION_COOKIE_HTTPONLY": current_app.config.get("SESSION_COOKIE_HTTPONLY"),
        "SESSION_COOKIE_SAMESITE": current_app.config.get("SESSION_COOKIE_SAMESITE"),
    }

    # Check if session directory exists and is writable
    session_dir = current_app.config.get("SESSION_FILE_DIR")
    dir_status = {
        "exists": os.path.exists(session_dir) if session_dir else False,
        "is_dir": os.path.isdir(session_dir) if session_dir else False,
        "writable": (
            os.access(session_dir, os.W_OK)
            if session_dir and os.path.exists(session_dir)
            else False
        ),
    }

    # Get request information
    request_info = {
        "scheme": request.scheme,
        "is_secure": request.is_secure,
        "host": request.host,
        "cookies": {key: request.cookies.get(key) for key in request.cookies.keys()},
        "headers": {key: value for key, value in request.headers.items()},
    }

    return jsonify(
        {
            "session_data": session_data,
            "session_config": session_config,
            "session_directory": dir_status,
            "request_info": request_info,
        }
    )


@api.route("/session-demo")
def session_demo():
    """
    Serve the session management demo page.
    This page demonstrates how to handle session cleanup when a user leaves the application.
    """
    return send_from_directory(current_app.static_folder, "session_demo.html")


@api.route("/session/<session_id>", methods=["GET"])
def get_session(session_id):
    """
    Get session data for a specific session ID.
    This endpoint allows filtering sessions by ID from the server side.

    Args:
        session_id (str): The session ID to retrieve

    Returns:
        JSON with session information or 404 if session not found
    """
    # Get session data for the specified ID
    session_data = get_session_by_id(session_id)

    # If session not found, return 404
    if not session_data:
        return (
            jsonify(
                {"success": False, "message": f"Session with ID {session_id} not found"}
            ),
            404,
        )

    # Return session data
    return jsonify({"success": True, "session": session_data})


@api.route("/sessions", methods=["GET"])
def list_sessions():
    """
    List all active sessions with their basic information.
    Useful for session management and debugging.

    Returns:
        JSON with list of all sessions and their metadata
    """
    try:
        # Get configuration paths
        session_folder = current_app.config["SESSION_FOLDER"]
        upload_folder = current_app.config["UPLOAD_FOLDER"]
        result_folder = current_app.config["RESULT_FOLDER"]

        sessions = []

        # Get all session files
        if os.path.exists(session_folder):
            session_files = [
                f for f in os.listdir(session_folder) if f.endswith(".json")
            ]

            for session_file in session_files:
                try:
                    # Extract session ID from filename
                    session_id = session_file.replace(".json", "")

                    # Read session data
                    session_data = read_session_data(session_id)

                    if session_data:
                        # Check directory existence
                        session_upload_dir = os.path.join(upload_folder, session_id)
                        session_result_dir = os.path.join(result_folder, session_id)

                        # Calculate directory sizes
                        upload_size = (
                            get_directory_size(session_upload_dir)
                            if os.path.exists(session_upload_dir)
                            else 0
                        )
                        result_size = (
                            get_directory_size(session_result_dir)
                            if os.path.exists(session_result_dir)
                            else 0
                        )

                        session_info = {
                            "session_id": session_id,
                            "created_at": session_data.get("created_at"),
                            "last_accessed": session_data.get("last_accessed"),
                            "has_upload_dir": os.path.exists(session_upload_dir),
                            "has_result_dir": os.path.exists(session_result_dir),
                            "upload_size_bytes": upload_size,
                            "result_size_bytes": result_size,
                            "total_size_bytes": upload_size + result_size,
                            "data_keys": (
                                list(session_data.get("data", {}).keys())
                                if "data" in session_data
                                else []
                            ),
                        }

                        sessions.append(session_info)

                except Exception as e:
                    # Add error entry for problematic sessions
                    sessions.append(
                        {
                            "session_id": session_file.replace(".json", ""),
                            "error": f"Error reading session: {str(e)}",
                        }
                    )

        # Sort sessions by last_accessed (most recent first)
        sessions.sort(key=lambda x: x.get("last_accessed", 0), reverse=True)

        # Calculate totals
        total_sessions = len(sessions)
        total_size = sum(s.get("total_size_bytes", 0) for s in sessions)

        return jsonify(
            {
                "success": True,
                "sessions": sessions,
                "total_sessions": total_sessions,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
            }
        )

    except Exception as e:
        print(f"Error listing sessions: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error listing sessions: {str(e)}"}),
            500,
        )


def get_directory_size(directory_path):
    """Calculate the total size of a directory in bytes"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(directory_path):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
    except Exception:
        pass
    return total_size


@api.route("/clear-session", methods=["POST"])
def clear_session():
    """
    Clear session data and remove associated files.

    Supports two modes:
    1. Clear specific session: Provide session_id parameter
    2. Clear all sessions: Set clear_all=true parameter

    Query Parameters:
        session_id (str, optional): Specific session ID to clear
        clear_all (bool, optional): Set to 'true' to clear all sessions
    """
    try:
        # Get parameters from request
        session_id = request.args.get("session_id")
        clear_all = request.args.get("clear_all", "").lower() == "true"

        # Check if we should clear all sessions
        if clear_all:
            return clear_all_sessions()

        # If no session ID provided and not clearing all, return error
        if not session_id:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "No session ID provided. Use session_id parameter for specific session or clear_all=true for all sessions.",
                    }
                ),
                400,
            )

        # Clear specific session
        return clear_specific_session(session_id)

    except Exception as e:
        print(f"Error in clear_session: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error clearing session: {str(e)}"}),
            500,
        )


def clear_specific_session(session_id):
    """Clear a specific session by ID"""
    try:
        # Check if session exists
        session_data = read_session_data(session_id)
        if not session_data:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Session with ID {session_id} not found",
                    }
                ),
                404,
            )

        # Get paths to session directories
        upload_folder = current_app.config["UPLOAD_FOLDER"]
        result_folder = current_app.config["RESULT_FOLDER"]
        session_upload_dir = os.path.join(upload_folder, session_id)
        session_result_dir = os.path.join(result_folder, session_id)

        # Delete session directories if they exist
        dirs_removed = []

        if os.path.exists(session_upload_dir):
            try:
                shutil.rmtree(session_upload_dir)
                dirs_removed.append(session_upload_dir)
            except Exception as e:
                print(f"Error removing upload directory: {str(e)}")

        if os.path.exists(session_result_dir):
            try:
                shutil.rmtree(session_result_dir)
                dirs_removed.append(session_result_dir)
            except Exception as e:
                print(f"Error removing result directory: {str(e)}")

        # Delete session file
        delete_session(session_id)

        return jsonify(
            {
                "success": True,
                "message": f"Session {session_id} cleared successfully",
                "session_id": session_id,
                "directories_removed": dirs_removed,
            }
        )

    except Exception as e:
        print(f"Error clearing specific session {session_id}: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error clearing session {session_id}: {str(e)}",
                }
            ),
            500,
        )


def clear_all_sessions():
    """Clear all sessions and their associated files"""
    try:
        # Get configuration paths
        session_folder = current_app.config["SESSION_FOLDER"]
        upload_folder = current_app.config["UPLOAD_FOLDER"]
        result_folder = current_app.config["RESULT_FOLDER"]

        cleared_sessions = []
        dirs_removed = []
        errors = []

        # Get all session files
        if os.path.exists(session_folder):
            session_files = [
                f for f in os.listdir(session_folder) if f.endswith(".json")
            ]

            for session_file in session_files:
                try:
                    # Extract session ID from filename
                    session_id = session_file.replace(".json", "")

                    # Delete session directories
                    session_upload_dir = os.path.join(upload_folder, session_id)
                    session_result_dir = os.path.join(result_folder, session_id)

                    if os.path.exists(session_upload_dir):
                        try:
                            shutil.rmtree(session_upload_dir)
                            dirs_removed.append(session_upload_dir)
                        except Exception as e:
                            errors.append(
                                f"Error removing upload directory for {session_id}: {str(e)}"
                            )

                    if os.path.exists(session_result_dir):
                        try:
                            shutil.rmtree(session_result_dir)
                            dirs_removed.append(session_result_dir)
                        except Exception as e:
                            errors.append(
                                f"Error removing result directory for {session_id}: {str(e)}"
                            )

                    # Delete session file
                    session_file_path = os.path.join(session_folder, session_file)
                    try:
                        os.remove(session_file_path)
                        cleared_sessions.append(session_id)
                    except Exception as e:
                        errors.append(
                            f"Error removing session file for {session_id}: {str(e)}"
                        )

                except Exception as e:
                    errors.append(f"Error processing session {session_file}: {str(e)}")

        # Also clean up any orphaned directories (directories without session files)
        for folder in [upload_folder, result_folder]:
            if os.path.exists(folder):
                try:
                    for item in os.listdir(folder):
                        item_path = os.path.join(folder, item)
                        if os.path.isdir(item_path):
                            # Check if this is a session directory (10-character hex string)
                            if len(item) == 10 and all(
                                c in "0123456789abcdef" for c in item.lower()
                            ):
                                # Check if session file exists
                                session_file_path = os.path.join(
                                    session_folder, f"{item}.json"
                                )
                                if not os.path.exists(session_file_path):
                                    # Orphaned directory, remove it
                                    try:
                                        shutil.rmtree(item_path)
                                        dirs_removed.append(item_path)
                                    except Exception as e:
                                        errors.append(
                                            f"Error removing orphaned directory {item_path}: {str(e)}"
                                        )
                except Exception as e:
                    errors.append(f"Error scanning {folder}: {str(e)}")

        # Prepare response
        response_data = {
            "success": True,
            "message": f"Cleared {len(cleared_sessions)} sessions successfully",
            "cleared_sessions": cleared_sessions,
            "directories_removed": dirs_removed,
            "total_sessions_cleared": len(cleared_sessions),
            "total_directories_removed": len(dirs_removed),
        }

        if errors:
            response_data["warnings"] = errors
            response_data["message"] += f" (with {len(errors)} warnings)"

        return jsonify(response_data)

    except Exception as e:
        print(f"Error clearing all sessions: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error clearing all sessions: {str(e)}"}
            ),
            500,
        )
