# Text Analysis System User Guide

## Introduction

This guide will help you use the Text Analysis System to analyze text data from CSV and Excel files. The system provides powerful natural language processing capabilities including word frequency analysis, word cloud generation, and topic modeling.

## Getting Started

### Accessing the System

1. Open your web browser and navigate to the system URL:
   - Local development: `http://localhost:7000`
   - Deployed version: Your deployment URL

2. You will see the main interface with options for uploading files and configuring analysis.

### Uploading Data

1. Click on the "Choose File" button or drag and drop a file into the upload area.
2. Supported file formats:
   - CSV (.csv)
   - Excel (.xlsx, .xls)
3. After uploading, the system will display the columns available in your file.

### Selecting Columns

1. From the dropdown menu, select the column containing the text you want to analyze.
2. For multiple columns, you can select them to combine their text data.

## Analysis Features

The system offers three main types of analysis:

1. **Word Frequency Analysis**
2. **Word Cloud Generation**
3. **LDA Topic Modeling**

### Word Frequency Analysis

This feature counts the frequency of words in your text data.

#### Steps:

1. Upload your file and select the text column.
2. Choose "Word Frequency Analysis" from the analysis options.
3. Configure the analysis parameters:
   - **Language**: Select Korean or English
   - **Analyzer**: Choose the morphological analyzer (Okt, Hannanum, Kkma, or Komoran for Korean; spaCy for English)
   - **Part of Speech**: Select which parts of speech to include (nouns, verbs, adjectives, etc.)
   - **Minimum Word Length**: Set the minimum character length for words to include
4. Click "Analyze" to start the process.
5. Results will display:
   - A table of words and their frequencies
   - A word cloud visualization
   - Option to download results as CSV

#### Word Selection Modes:

- **Top N Words**: Automatically selects the most frequent words
- **Manual Selection**: Allows you to choose specific words from the frequency list

### Word Cloud Generation

Word clouds visually represent word frequency, with larger words appearing more frequently in the text.

#### Steps:

1. After performing word frequency analysis, you can customize the word cloud:
   - **Shape**: Choose from rectangle, circle, or other shapes
   - **Color Scheme**: Select from various color palettes (viridis, plasma, etc.)
   - **Word Selection**: Choose which words to include
2. Click "Generate Word Cloud" to create the visualization.
3. You can download the word cloud image for use in presentations or reports.

#### Editing Words:

1. In the word list, you can select or deselect words to include in the word cloud.
2. Click "Apply Selection" to update the word cloud with your selected words.
3. You can also edit the word list directly and regenerate the word cloud.

### LDA Topic Modeling

Latent Dirichlet Allocation (LDA) is a technique that identifies topics within text data.

#### Steps:

1. Upload your file and select the text column.
2. Choose "LDA Topic Modeling" from the analysis options.
3. Configure the modeling parameters:
   - **Min/Max Topics**: Set the range of topics to test
   - **Minimum Document Frequency**: Minimum number of documents a word must appear in
   - **Maximum Document Frequency**: Maximum percentage of documents a word can appear in
   - **Network Style**: Visual style for the topic network
   - **Chart Style**: Visual style for the topic charts
4. Click "Process" to start the analysis.
5. Results will display:
   - Optimal number of topics based on perplexity and coherence scores
   - Topic-word distributions
   - Network visualization showing relationships between topics
   - Word cloud visualization
   - Interactive pyLDAvis visualization

#### Editing Keywords:

1. Select a topic from the topic list.
2. You can edit keywords by:
   - Changing a word (click on a word and edit it)
   - Removing a word (click the "X" next to a word)
3. Click "Apply Changes" to update the visualizations with your edited keywords.

## Exporting Results

The system provides several options for exporting your analysis results:

### Word Frequency Analysis

- **CSV Export**: Download the word frequency data as a CSV file
- **Word Cloud Image**: Download the word cloud as a PNG image

### LDA Topic Modeling

- **CSV Export**: Download the topic-word distributions as a CSV file
- **Images**: Download the network visualization, word cloud, and topic charts as PNG images
- **HTML Report**: Generate a comprehensive HTML report with all visualizations and results
- **ZIP Package**: Download all results in a single ZIP file

## Advanced Features

### Custom Tokenization

For advanced users, the system allows customization of the tokenization process:

1. Select the language and analyzer.
2. Choose specific parts of speech to include.
3. Set minimum word length to filter out short words.

### Topic Number Optimization

The LDA analysis automatically tests different numbers of topics and suggests an optimal number based on:

1. **Perplexity Score**: Measures how well the model predicts new data
2. **Coherence Score**: Measures how semantically coherent the topics are

You can override the suggested number by:
1. Viewing the perplexity and coherence plots
2. Entering a manual topic number in the "Manual Topic Number" field
3. Clicking "Update Model" to regenerate the analysis with your chosen number of topics

## Tips for Better Results

1. **Data Preparation**:
   - Remove irrelevant text before uploading
   - Ensure text is in the correct encoding (UTF-8 recommended)
   - For large files, consider splitting into smaller chunks

2. **Language Selection**:
   - For Korean text, Okt (Open Korean Text) generally provides good results
   - For English text, spaCy is the recommended analyzer

3. **Part of Speech Selection**:
   - For topic analysis, focusing on nouns often gives clearer topics
   - For sentiment analysis, including adjectives and verbs can be helpful

4. **Topic Modeling**:
   - Start with the default range of topics (3-10)
   - Examine the perplexity and coherence plots to identify the optimal number
   - If topics seem too general or too specific, adjust the number manually

5. **Word Cloud Customization**:
   - Rectangle shape works well for most purposes
   - Circle shape can create more visually appealing results for presentations
   - Experiment with different color schemes for different effects

## Troubleshooting

### Common Issues

1. **File Upload Errors**:
   - Ensure your file is in CSV or Excel format
   - Check that the file size is under 16MB
   - Verify that the file is not corrupted

2. **Processing Errors**:
   - For large files, the processing may take longer
   - If processing fails, try with a smaller subset of data
   - Check that your text column contains valid text data

3. **Visualization Issues**:
   - If word clouds don't display, try a different browser
   - For Korean text, ensure the system has access to Korean fonts
   - If network visualizations are cluttered, try reducing the number of topics

### Getting Help

If you encounter issues not covered in this guide, please contact the system administrator or refer to the technical documentation.
