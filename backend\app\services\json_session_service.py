"""
JSON-based session management for the application.
This module provides functions for managing user sessions using JSON files.
"""

import json
import os
import time
import uuid

from flask import current_app, request


# Session folder path
def get_session_folder():
    """Get the path to the session folder"""
    return current_app.config["SESSION_FOLDER"]


def ensure_session_folder():
    """Ensure the session folder exists"""
    session_folder = get_session_folder()
    if not os.path.exists(session_folder):
        os.makedirs(session_folder)
    return session_folder


def get_session_file_path(session_id):
    """Get the path to the session file"""
    return os.path.join(get_session_folder(), f"{session_id}.json")


def create_session():
    """Create a new session"""
    # Generate a unique session ID (10 characters)
    session_id = str(uuid.uuid4().hex[:10])

    # Create session directories
    ensure_session_directories(session_id)

    # Create session file with initial data
    session_data = {
        "session_id": session_id,
        "created_at": time.time(),
        "last_accessed": time.time(),
        "data": {},
    }

    # Save session data
    save_session_data(session_id, session_data)

    return session_id


def get_session(session_id=None):
    """
    Get session data for the specified session ID or from request parameters.

    Args:
        session_id (str, optional): The session ID to retrieve. If None, tries to get from request.

    Returns:
        tuple: (session_id, session_data) or (None, None) if session not found
    """
    # If no session_id provided, try to get from request
    if session_id is None:
        session_id = request.args.get("session_id")

    # If still no session_id, return None
    if not session_id:
        return None, None

    # Get session data
    session_data = read_session_data(session_id)
    if session_data:
        # Update last accessed time
        session_data["last_accessed"] = time.time()
        save_session_data(session_id, session_data)
        return session_id, session_data

    return None, None


def get_or_create_session(session_id=None):
    """
    Get an existing session or create a new one.

    Args:
        session_id (str, optional): The session ID to retrieve. If None, tries to get from request.

    Returns:
        tuple: (session_id, session_data)
    """
    # Try to get existing session
    session_id, session_data = get_session(session_id)

    # If no session found, create a new one
    if not session_id:
        session_id = create_session()
        session_data = read_session_data(session_id)

    return session_id, session_data


def read_session_data(session_id):
    """
    Read session data from file.

    Args:
        session_id (str): The session ID

    Returns:
        dict: Session data or None if not found
    """
    session_file = get_session_file_path(session_id)

    if not os.path.exists(session_file):
        return None

    try:
        with open(session_file, "r") as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return None


def save_session_data(session_id, session_data):
    """
    Save session data to file.

    Args:
        session_id (str): The session ID
        session_data (dict): The session data to save

    Returns:
        bool: True if successful, False otherwise
    """
    ensure_session_folder()
    session_file = get_session_file_path(session_id)

    try:
        with open(session_file, "w") as f:
            json.dump(session_data, f, indent=2)
        return True
    except Exception:
        return False


def set_session_value(session_id, key, value):
    """
    Set a value in the session data.

    Args:
        session_id (str): The session ID
        key (str): The key to set
        value: The value to set

    Returns:
        bool: True if successful, False otherwise
    """
    session_data = read_session_data(session_id)
    if not session_data:
        return False

    # Update session data
    if "data" not in session_data:
        session_data["data"] = {}

    session_data["data"][key] = value
    session_data["last_accessed"] = time.time()

    return save_session_data(session_id, session_data)


def get_session_value(session_id, key, default=None):
    """
    Get a value from the session data.

    Args:
        session_id (str): The session ID
        key (str): The key to get
        default: The default value to return if key not found

    Returns:
        The value or default if not found
    """
    session_data = read_session_data(session_id)
    if not session_data or "data" not in session_data:
        return default

    return session_data["data"].get(key, default)


def delete_session(session_id):
    """
    Delete a session.

    Args:
        session_id (str): The session ID

    Returns:
        bool: True if successful, False otherwise
    """
    session_file = get_session_file_path(session_id)

    if os.path.exists(session_file):
        try:
            os.remove(session_file)
            return True
        except Exception:
            return False

    return True  # Session already doesn't exist


def ensure_session_directories(session_id):
    """
    Ensure that the necessary directories for the session exist.

    Args:
        session_id (str): The session ID
    """
    try:
        # Create session folder
        ensure_session_folder()

        # Create upload directory
        upload_folder = current_app.config["UPLOAD_FOLDER"]
        session_upload_dir = os.path.join(upload_folder, session_id)
        if not os.path.exists(session_upload_dir):
            os.makedirs(session_upload_dir)

        # Create result directory
        result_folder = current_app.config["RESULT_FOLDER"]
        session_result_dir = os.path.join(result_folder, session_id)
        if not os.path.exists(session_result_dir):
            os.makedirs(session_result_dir)

        # Create subdirectories for different types of results using config values
        subdirs = [
            current_app.config["TOPIC_MODELS_FOLDER"],
            current_app.config["WORD_CLOUD_FOLDER"],
            current_app.config["WORD_FREQ_FOLDER"],
        ]
        for subdir in subdirs:
            subdir_path = os.path.join(session_result_dir, subdir)
            if not os.path.exists(subdir_path):
                os.makedirs(subdir_path)

        return True
    except Exception as e:
        print(f"Error creating session directories: {str(e)}")
        return False
