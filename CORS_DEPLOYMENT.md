# CORS Configuration for Deployment

This document explains how to configure Cross-Origin Resource Sharing (CORS) when deploying your application to Render or other hosting platforms.

## What is CORS?

Cross-Origin Resource Sharing (CORS) is a security feature implemented by browsers that restricts web pages from making requests to a different domain than the one that served the original page. This is a security measure to prevent malicious websites from making unauthorized requests to other websites on behalf of the user.

## CORS Configuration in This Application

This application uses Flask-CORS to handle CORS headers. The configuration has been updated to support both development and production environments.

### How It Works

1. The application reads a `CORS_ALLOWED_ORIGINS` environment variable to determine which origins are allowed to access the API.
2. If this variable is set to `*`, all origins are allowed (not recommended for production).
3. If specific origins are provided (e.g., `https://your-frontend-app.onrender.com`), only those origins will be allowed.
4. Multiple origins can be specified as a comma-separated list.

## Deployment Configuration

### Setting Up CORS on Render

When deploying to Render, you need to configure the `CORS_ALLOWED_ORIGINS` environment variable:

1. Go to your Render dashboard and select your backend service.
2. Navigate to the "Environment" tab.
3. Add a new environment variable:
   - Key: `CORS_ALLOWED_ORIGINS`
   - Value: The URL of your frontend application (e.g., `https://your-frontend-app.onrender.com`)
   - If you have multiple frontend applications, separate them with commas (e.g., `https://app1.com,https://app2.com`)

### Example Environment Variables for Render

```
CORS_ALLOWED_ORIGINS=https://your-frontend-app.onrender.com
```

Or for multiple origins:

```
CORS_ALLOWED_ORIGINS=https://your-frontend-app.onrender.com,https://your-other-app.com
```

## Testing CORS Configuration

To test if your CORS configuration is working correctly:

1. Deploy your backend to Render with the appropriate CORS settings.
2. Deploy your frontend to its hosting platform.
3. Open your frontend application in a browser and check the browser's developer console for any CORS-related errors.
4. If you see CORS errors, verify that the `CORS_ALLOWED_ORIGINS` environment variable is set correctly.

## Troubleshooting CORS Issues

If you're still experiencing CORS issues after configuring the environment variables:

1. **Check the request headers**: Make sure your frontend requests include the appropriate headers.
2. **Verify credentials handling**: If your requests include credentials (cookies, HTTP authentication), make sure `withCredentials: true` is set in your frontend API calls.
3. **Check for preflight requests**: For complex requests, browsers send a preflight OPTIONS request. Ensure your server correctly responds to these requests.
4. **Inspect network traffic**: Use the browser's developer tools to inspect the network traffic and check the response headers.

## Local Development

For local development, you can set `CORS_ALLOWED_ORIGINS=*` to allow all origins, but this should not be used in production for security reasons.
