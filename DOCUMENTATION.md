# Text Analysis System Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [API Reference](#api-reference)
6. [Features](#features)
7. [Session Management](#session-management)
8. [Deployment](#deployment)
9. [Troubleshooting](#troubleshooting)

## System Overview

This system is a comprehensive text analysis platform that provides natural language processing (NLP) capabilities for analyzing text data from CSV and Excel files. The system offers multiple analysis methods including:

- **Word Frequency Analysis**: Analyzes the frequency of words in text data
- **Word Cloud Generation**: Creates visual representations of word frequency
- **LDA Topic Modeling**: Identifies topics within text data using Latent Dirichlet Allocation
- **Network Visualization**: Visualizes relationships between topics and words

The system supports both Korean and English text analysis with various morphological analyzers and provides an interactive web interface for users to upload data, configure analysis parameters, and visualize results.

## Architecture

### Backend Components

The system is built using a Flask-based backend with the following components:

1. **Core Application**
   - `run.py`: Entry point for the Flask application
   - `app/__init__.py`: Application factory and configuration
   - `app/config.py`: Configuration settings

2. **Blueprints (API Routes)**
   - `upload`: Handles file uploads and column selection
   - `data_process`: Processes text data and performs NLP operations
   - `analyse`: Performs analysis operations (frequency, LDA, etc.)

3. **Services**
   - `file_processor.py`: Handles file operations (reading, writing)
   - `nlp_service.py`: Provides NLP functionality (tokenization, analysis)
   - `progress_service.py`: Tracks processing progress

4. **NLP Components**
   - KoNLPy for Korean text analysis (Okt, Hannanum, Kkma, Komoran)
   - spaCy for English text analysis
   - Gensim for LDA topic modeling
   - WordCloud for generating word cloud visualizations

### Frontend Components

The system provides HTML templates with JavaScript for the user interface:

- File upload and column selection
- Analysis parameter configuration
- Interactive visualizations (word clouds, topic networks)
- Result display and export

### Data Flow

1. User uploads a CSV or Excel file
2. System extracts columns and allows user to select text columns
3. User configures analysis parameters
4. System processes the text data using selected NLP tools
5. Results are generated and displayed to the user
6. User can interact with and export the results

## Installation

### Prerequisites

- Python 3.12+
- Java JDK (required for KoNLPy)
- Node.js and npm (for frontend development, optional)

### Docker Installation

The easiest way to run the system is using Docker:

```bash
# Clone the repository
git clone <repository-url>
cd analysis_project

# Build the Docker image
docker build -t text-analysis-app ./analysis_app

# Run the container
docker run -p 7000:7000 -e FLASK_SECRET_KEY=your_secret_key text-analysis-app
```

### Manual Installation

```bash
# Clone the repository
git clone <repository-url>
cd analysis_project

# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r analysis_app/requirements.txt

# Download spaCy English model
python -m spacy download en_core_web_sm

# Run the application
cd analysis_app/backend
python run.py
```

## Configuration

### Environment Variables

The application uses the following environment variables:

- `FLASK_SECRET_KEY`: Secret key for session encryption
- `FLASK_ENV`: Environment (development, production)
- `CORS_ALLOWED_ORIGINS`: Comma-separated list of allowed origins for CORS
- `SESSION_COOKIE_HTTPONLY`: Whether to set HttpOnly flag on cookies (true/false)
- `SESSION_COOKIE_SECURE`: Whether to set Secure flag on cookies (true/false)
- `SWAGGER_JSON_URL`: URL to the Swagger JSON file

### Configuration File

The main configuration is in `app/config.py`:

```python
class Config:
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    UPLOAD_FOLDER = os.path.join(BASE_DIR, '..', 'uploads')
    RESULT_FOLDER = os.path.join(BASE_DIR, '..', 'results')
    DOWNLOAD_FOLDER = os.path.join(BASE_DIR, '..', 'downloads')
    TOPIC_MODELS_FOLDER = 'topic_images'
    WORD_CLOUD_FOLDER = 'word_clouds'
    WORD_FREQ_FOLDER = 'word_freq'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB limit
    DEBUG = True
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY')
    SWAGGER_URL = '/api/docs'
    API_URL = os.getenv('SWAGGER_JSON_URL', "http://localhost:7000/static/swagger.json")
    FONT_PATH = os.path.join(os.path.dirname(__file__), 'static', 'malgun.ttf')
```

## API Reference

The system provides a RESTful API for interacting with the analysis functionality. API documentation is available through Swagger UI at `/api/docs` when the application is running.

### Key Endpoints

#### File Upload and Processing

- **POST** `/api/upload`
  - Uploads a CSV or Excel file
  - Returns file columns and creates a session

- **GET** `/api/columns`
  - Returns columns from the uploaded file

- **POST** `/api/process`
  - Processes the file with specified NLP options
  - Returns a task ID for tracking progress

- **GET** `/api/progress/<task_id>`
  - Returns the progress of a processing task

#### Analysis

- **POST** `/api/analyse/analyze`
  - Performs frequency analysis on the text data
  - Returns word frequency data and word cloud

- **POST** `/api/analyse/wordcloud`
  - Generates a word cloud from selected words
  - Returns the word cloud image path

- **POST** `/api/analyse/process`
  - Performs LDA topic modeling
  - Returns topic model results and visualizations

- **POST** `/api/analyse/edit_keywords`
  - Edits keywords in topic models
  - Returns updated visualizations

#### Results

- **GET** `/api/results/<subpath>`
  - Serves result files (images, CSV, etc.)

- **GET** `/api/download/<filename>`
  - Downloads processed files

#### Session Management

- **POST** `/api/clear-session`
  - Clears all session data and removes associated files
  - Returns success status and list of removed directories

- **GET** `/api/session-demo`
  - Serves a demo page showing how to implement session cleanup

## Features

### Text Processing

- **Tokenization**: Breaks text into words or tokens
- **Part-of-Speech Tagging**: Identifies parts of speech (nouns, verbs, etc.)
- **Stopword Removal**: Removes common words that don't add meaning
- **Special Character Removal**: Cleans text of special characters and numbers

### Analysis Methods

1. **Word Frequency Analysis**
   - Counts word occurrences in text
   - Provides sorted word lists by frequency
   - Generates CSV output with word statistics

2. **Word Cloud Generation**
   - Creates visual representations of word frequency
   - Supports different shapes (rectangle, circle, etc.)
   - Supports different color schemes

3. **LDA Topic Modeling**
   - Identifies topics in text data
   - Provides topic-word distributions
   - Allows for topic number optimization
   - Supports keyword editing

4. **Visualization**
   - Topic networks showing relationships between topics
   - Word clouds for visual representation of word frequency
   - Bar charts showing topic-word distributions
   - Interactive pyLDAvis visualization

### User Interface

- File upload and column selection
- Analysis parameter configuration
- Interactive result display
- Result export (CSV, PNG, HTML report)

## Session Management

The system includes robust session management to handle user data and cleanup when users leave the application.

### Session Structure

Each user session includes:

1. **Session Data**: Stored in Flask's session object
   - Session ID (unique identifier)
   - Uploaded file information
   - Analysis parameters
   - Results references

2. **File Storage**: Organized in session-specific directories
   - `/uploads/{session_id}/` - Contains uploaded files
   - `/results/{session_id}/` - Contains analysis results
     - `/results/{session_id}/topic_images/` - Topic visualization images
     - `/results/{session_id}/word_clouds/` - Word cloud images
     - `/results/{session_id}/word_freq/` - Word frequency data

### Session Cleanup

The system provides an API endpoint to clear session data when a user leaves:

- **POST** `/api/clear-session`
  - Clears all session data and removes associated files
  - Returns success status and list of removed directories

### Frontend Integration

To implement session cleanup in frontend applications:

1. **Handle Page Unload**:
   ```javascript
   // Show confirmation dialog when user tries to leave
   window.addEventListener('beforeunload', function(e) {
     const confirmationMessage = 'You are about to leave this page. Your session data will be deleted. Continue?';
     e.returnValue = confirmationMessage;
     return confirmationMessage;
   });

   // If user confirms leaving, clear the session
   window.addEventListener('unload', function() {
     navigator.sendBeacon('/api/clear-session', JSON.stringify({}));
   });
   ```

2. **Explicit Cleanup**:
   ```javascript
   async function clearSessionData() {
     const response = await fetch('/api/clear-session', {
       method: 'POST',
       credentials: 'include',
       headers: { 'Content-Type': 'application/json' }
     });
     return await response.json();
   }
   ```

For more detailed information, see the [SESSION_MANAGEMENT.md](SESSION_MANAGEMENT.md) file.

## Deployment

### Docker Deployment

The system includes a Dockerfile for containerized deployment:

```dockerfile
# Use an official Python runtime as a parent image
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt /app/
RUN apt-get update && apt-get install -y build-essential gcc python3-dev libpq-dev default-jdk
RUN pip install -r requirements.txt

# Download spaCy English model
RUN python -m spacy download en_core_web_sm

# Copy the application code
COPY ./backend /app/

# Expose the port
EXPOSE 7000

# Set environment variables
ENV FLASK_APP=run.py
ENV FLASK_RUN_PORT=7000
ENV FLASK_ENV=production
ENV FLASK_RUN_HOST=0.0.0.0
ENV CORS_ALLOWED_ORIGINS=*

# Run the application
CMD ["flask", "run", "--host=0.0.0.0", "--port=7000"]
```

### Render Deployment

To deploy on Render:

1. Create a new Web Service
2. Connect your repository
3. Set the following:
   - Build Command: `pip install -r analysis_app/requirements.txt && python -m spacy download en_core_web_sm`
   - Start Command: `cd analysis_app/backend && python run.py`
4. Add environment variables:
   - `FLASK_SECRET_KEY`: A secure random string
   - `CORS_ALLOWED_ORIGINS`: Your frontend URL
   - `FLASK_ENV`: production

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Set the `CORS_ALLOWED_ORIGINS` environment variable to your frontend URL
   - For multiple origins, use a comma-separated list
   - See `CORS_DEPLOYMENT.md` for detailed instructions

2. **File Upload Issues**
   - Check that the upload directory exists and is writable
   - Verify that the file size is under the 16MB limit
   - Ensure the file format is CSV or Excel (.xlsx, .xls)

3. **NLP Processing Errors**
   - For Korean text, ensure KoNLPy is properly installed with Java
   - For English text, verify that the spaCy model is downloaded
   - For large files, increase processing timeout or use smaller datasets

4. **Visualization Issues**
   - Ensure matplotlib and wordcloud dependencies are installed
   - Check that the font file exists (especially for non-Latin characters)
   - Verify that the result directories are writable

### Logs

- Application logs are output to the console
- In production, consider setting up a logging service

### Support

For additional support, please refer to the project repository or contact the development team.
