from flask import Blueprint

freq_bp = Blueprint("analysis", __name__, url_prefix="/api")
lda_bp = Blueprint("lda", __name__, url_prefix="/api")
tfidf_bp = Blueprint("tfidf", __name__, url_prefix="/api/tfidf")
ngram_bp = Blueprint("ngram", __name__, url_prefix="/api/ngram")
connet_bp = Blueprint("connet", __name__, url_prefix="/api/connet")
bert_bp = Blueprint("bert", __name__, url_prefix="/api/bert")
sentrans_bp = Blueprint("sentrans", __name__, url_prefix="/api/sentrans")



from . import routes, tfidf_routes, lda_routes, ngram_routes, concornet_routes, bert_routes, sentrans_routes
