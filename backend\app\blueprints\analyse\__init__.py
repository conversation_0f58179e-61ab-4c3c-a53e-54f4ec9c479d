from flask import Blueprint

freq_bp = Blueprint("analysis", __name__)
lda_bp = Blueprint("lda", __name__)
tfidf_bp = Blueprint("tfidf", __name__)
ngram_bp = Blueprint("ngram", __name__)
connet_bp = Blueprint("connet", __name__)
bert_bp = Blueprint("bert", __name__)
sentrans_bp = Blueprint("sentrans", __name__)



from . import routes, tfidf_routes, lda_routes, ngram_routes, concornet_routes, bert_routes, sentrans_routes
