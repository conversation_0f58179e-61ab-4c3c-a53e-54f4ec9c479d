import os

import pandas as pd
from flask import current_app, jsonify, request, send_from_directory
from werkzeug.utils import secure_filename

from ...services.file_processor import read_file
from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.session_service import (get_or_create_session_id,
                                         require_session)
from . import upload_bp

# configuration constants from app.config via current_app.config
ALLOWED_EXTENSIONS = {"xlsx", "csv"}


# Configure CORS for React frontend with HTTPS support
@upload_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    return response


def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


@upload_bp.route("/upload", methods=["POST"])
@require_session
def upload_file():
    """
    Upload a file and associate it with the current session.
    Creates a new session if one doesn't exist.
    """
    # Get or create session ID
    session_id = get_or_create_session_id()

    try:
        if "file" not in request.files:
            return (
                jsonify({"success": False, "error": "파일이 전송되지 않았습니다."}),
                400,
            )

        file = request.files["file"]
        if file.filename == "":
            return (
                jsonify({"success": False, "error": "파일이 선택되지 않았습니다."}),
                400,
            )

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            upload_folder = current_app.config["UPLOAD_FOLDER"]
            session_folder = os.path.join(upload_folder, session_id)

            # Save the file to the session-specific folder
            filepath = os.path.join(session_folder, filename)
            file.save(filepath)

            # read file into DataFrame
            df = read_file(filepath)

            columns = df.columns.tolist()
            # Store filename in session data
            set_session_value(session_id, "uploaded_file", filename)
            return jsonify(
                {
                    "success": True,
                    "filename": filename,
                    "columns": columns,
                    "session_id": session_id,
                }
            )

        return (
            jsonify({"success": False, "error": "허용되지 않는 파일 형식입니다."}),
            400,
        )

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@upload_bp.route("/select-columns", methods=["POST"])
@require_session
def select_columns():
    """
    Select columns from the uploaded file for processing.
    Requires an active session with an uploaded file.
    """
    try:
        # Ensure we have an active session
        session_id = get_or_create_session_id()

        # Get uploaded file from session data
        uploaded_file = get_session_value(session_id, "uploaded_file")
        if uploaded_file is None:
            return (
                jsonify(
                    {"error": "No file uploaded in this session", "success": False}
                ),
                400,
            )

        # Get data from session
        session_data = get_session_value(session_id, "data")
        if session_data:
            data = pd.DataFrame.from_dict(session_data)
            selected_columns = data.get("columns", [])
        else:
            selected_columns = request.json.get("columns", [])

        filename = uploaded_file
        if not filename or not selected_columns:
            return (
                jsonify(
                    {"success": False, "error": "파일명과 선택된 컬럼이 필요합니다."}
                ),
                400,
            )

        upload_folder = current_app.config["UPLOAD_FOLDER"]
        session_folder = os.path.join(upload_folder, session_id)
        filepath = os.path.join(session_folder, filename)

        if not os.path.exists(filepath):
            return jsonify({"success": False, "error": "파일을 찾을 수 없습니다."}), 404

        if filename.endswith(".csv"):
            df = pd.read_csv(filepath)
        else:
            df = pd.read_excel(filepath)

        combined_text = df[selected_columns].astype(str).agg(" ".join, axis=1)
        result_df = pd.DataFrame({"Combined_Text": combined_text})

        # Store data in session
        set_session_value(session_id, "columns", selected_columns)
        set_session_value(session_id, "data", result_df.to_dict())

        data_info = {
            "row_count": len(result_df),
            "column_count": len(selected_columns),
            "original_columns": selected_columns,
            "preview": result_df.head(10).to_dict("records"),
        }

        return jsonify({"success": True, "data_info": data_info})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
