# Deployment Guide

This guide provides instructions for deploying the Text Analysis System to various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Deployment](#local-deployment)
3. [Docker Deployment](#docker-deployment)
4. [Render Deployment](#render-deployment)
5. [Environment Variables](#environment-variables)
6. [CORS Configuration](#cors-configuration)
7. [Security Considerations](#security-considerations)
8. [Performance Optimization](#performance-optimization)

## Prerequisites

Before deploying the system, ensure you have:

- Python 3.12 or higher
- Java JDK (required for KoNLPy)
- Git
- Docker (for containerized deployment)
- Access to a cloud platform (for cloud deployment)

## Local Deployment

### Step 1: Clone the Repository

```bash
git clone <repository-url>
cd analysis_project
```

### Step 2: Create a Virtual Environment

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

### Step 3: Install Dependencies

```bash
pip install -r analysis_app/requirements.txt
python -m spacy download en_core_web_sm
```

### Step 4: Set Environment Variables

```bash
# Linux/macOS
export FLASK_SECRET_KEY=your_secret_key
export FLASK_ENV=development

# Windows
set FLASK_SECRET_KEY=your_secret_key
set FLASK_ENV=development
```

### Step 5: Run the Application

```bash
cd analysis_app/backend
python run.py
```

The application will be available at `http://localhost:7000`.

## Docker Deployment

### Step 1: Build the Docker Image

```bash
cd analysis_project
docker build -t text-analysis-app ./analysis_app
```

### Step 2: Run the Docker Container

```bash
docker run -p 7000:7000 \
  -e FLASK_SECRET_KEY=your_secret_key \
  -e FLASK_ENV=production \
  -e CORS_ALLOWED_ORIGINS=* \
  text-analysis-app
```

The application will be available at `http://localhost:7000`.

### Step 3: (Optional) Persist Data

To persist uploaded files and results between container restarts:

```bash
docker run -p 7000:7000 \
  -e FLASK_SECRET_KEY=your_secret_key \
  -e FLASK_ENV=production \
  -e CORS_ALLOWED_ORIGINS=* \
  -v /path/on/host/uploads:/app/uploads \
  -v /path/on/host/results:/app/results \
  text-analysis-app
```

## Render Deployment

[Render](https://render.com/) is a cloud platform that makes it easy to deploy web applications.

### Step 1: Create a New Web Service

1. Sign in to your Render account
2. Click "New" and select "Web Service"
3. Connect your GitHub repository

### Step 2: Configure the Service

1. Name: Choose a name for your service
2. Environment: Select "Python"
3. Build Command: `pip install -r analysis_app/requirements.txt && python -m spacy download en_core_web_sm`
4. Start Command: `cd analysis_app/backend && python run.py`

### Step 3: Add Environment Variables

Add the following environment variables:

- `FLASK_SECRET_KEY`: A secure random string
- `FLASK_ENV`: production
- `CORS_ALLOWED_ORIGINS`: Your frontend URL (e.g., `https://your-frontend-app.onrender.com`)
- `SESSION_COOKIE_SECURE`: true
- `SESSION_COOKIE_HTTPONLY`: true

### Step 4: Deploy

Click "Create Web Service" to deploy your application.

### Step 5: Configure Persistent Disk (Optional)

For production use, you may want to add a persistent disk to store uploaded files and results:

1. Go to your web service dashboard
2. Click on "Disks" in the left sidebar
3. Click "Add Disk"
4. Set the mount path to `/app/data`
5. Choose an appropriate size for your needs

Then update your code to use this path for uploads and results.

## Environment Variables

The system uses the following environment variables:

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `FLASK_SECRET_KEY` | Secret key for session encryption | None | Yes |
| `FLASK_ENV` | Environment (development, production) | development | No |
| `FLASK_RUN_PORT` | Port to run the application on | 7000 | No |
| `FLASK_RUN_HOST` | Host to run the application on | 0.0.0.0 | No |
| `CORS_ALLOWED_ORIGINS` | Allowed origins for CORS | * | No |
| `SESSION_COOKIE_HTTPONLY` | Set HttpOnly flag on cookies | true | No |
| `SESSION_COOKIE_SECURE` | Set Secure flag on cookies | false | No |
| `SWAGGER_JSON_URL` | URL to the Swagger JSON file | http://localhost:7000/static/swagger.json | No |

## CORS Configuration

Cross-Origin Resource Sharing (CORS) is important when your frontend and backend are hosted on different domains.

### Basic Configuration

The system is configured to read the `CORS_ALLOWED_ORIGINS` environment variable:

- `*`: Allow all origins (not recommended for production)
- `https://example.com`: Allow a specific origin
- `https://app1.com,https://app2.com`: Allow multiple origins (comma-separated)

### Production Configuration

For production, set `CORS_ALLOWED_ORIGINS` to your frontend URL:

```bash
# Single origin
export CORS_ALLOWED_ORIGINS=https://your-frontend-app.com

# Multiple origins
export CORS_ALLOWED_ORIGINS=https://app1.com,https://app2.com
```

For more details, see the [CORS_DEPLOYMENT.md](CORS_DEPLOYMENT.md) file.

## Security Considerations

### Session Security

1. Set a strong `FLASK_SECRET_KEY` (at least 24 random characters)
2. In production, set `SESSION_COOKIE_SECURE=true` to ensure cookies are only sent over HTTPS
3. Set `SESSION_COOKIE_HTTPONLY=true` to prevent JavaScript access to cookies

### File Upload Security

1. The system limits file uploads to 16MB by default
2. Only CSV and Excel files are allowed
3. Uploaded files are stored in session-specific directories to prevent access to other users' files

### API Security

1. Use HTTPS in production
2. Set specific CORS origins rather than wildcard (`*`)
3. Consider adding rate limiting for production deployments

## Performance Optimization

### Memory Usage

The system can be memory-intensive, especially when processing large files or running LDA topic modeling. Consider:

1. Increasing container memory limits (for Docker/cloud deployments)
2. For Render, choose an appropriate instance type with sufficient memory
3. For very large datasets, consider preprocessing to reduce size before uploading

### Processing Speed

To improve processing speed:

1. Use a machine with multiple CPU cores
2. For cloud deployments, choose instances with good CPU performance
3. Consider implementing a task queue (like Celery) for very large processing jobs

### Disk Space

The system stores uploaded files and results. Ensure sufficient disk space:

1. For Docker, mount volumes with adequate space
2. For Render, add a persistent disk with appropriate size
3. Implement a cleanup routine to remove old files (not included by default)

## Monitoring and Maintenance

### Logs

The application logs to standard output. To view logs:

- Local: Check the console output
- Docker: `docker logs <container_id>`
- Render: Check the "Logs" tab in the Render dashboard

### Updates

To update the application:

1. Pull the latest code from the repository
2. Rebuild the Docker image (for Docker deployment)
3. Redeploy on Render (automatic if connected to GitHub with auto-deploy)
