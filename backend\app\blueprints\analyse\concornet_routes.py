import logging
import traceback
import os
import json
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import io
import traceback
import random
from flask import request, jsonify, jsonify, request, current_app

from ...services.json_session_service import get_session_value
from ...services.connet_services import connet_analysis
from ...services.nlp_service import get_file_path 
from ...services.session_service import get_or_create_session_id
from . import connet_bp

# Setup logging
logger = logging.getLogger(__name__)




@connet_bp.route('/analyze', methods=['POST'])
def analyze():
    try:
        # Get parameters from request
        session_id = get_or_create_session_id()
        params = request.form.to_dict()
        
        # Get parameters with defaults
        node_size_min = float(params.get('node_size_min', 4000))
        node_size_max = float(params.get('node_size_max', 8000))
        edge_width_min = float(params.get('edge_width_min', 1.0))
        edge_width_max = float(params.get('edge_width_max', 15.0))
        edge_color = params.get('edge_color', '#707070')
        edge_alpha = float(params.get('edge_alpha', 0.7))
        node_color = params.get('node_color', '#1f78b4')
        label_size = float(params.get('label_size', 18))
        label_color = params.get('label_color', '#000000')
        label_font_weight = params.get('label_font_weight', 'bold')
        label_background = params.get('label_background', 'true').lower() == 'true'
        layout_seed = int(params.get('layout_seed', random.randint(1, 10000)))
        layout_k = float(params.get('layout_k', 1.3))
        layout_iterations = int(params.get('layout_iterations', 200))
        edge_filter_percent = float(params.get('edge_filter_percent', 30))
        use_concor = params.get('use_concor', 'true').lower() == 'true'
        concor_n = int(params.get('concor_n', 3))
        concor_max_iter = int(params.get('concor_max_iter', 50))
        concor_convergence = float(params.get('concor_convergence', 0.95))
        node_repulsion = float(params.get('node_repulsion', 1.5))
        group_layout_quality = int(params.get('group_layout_quality', 100))
        intergroup_force = float(params.get('intergroup_force', 1.2))
        group_compactness = float(params.get('group_compactness', 0.8))
        column_name = params.get('column_name', None)
        top_word_count = int(params.get('top_word_count', 30))
        color_palette = params.get('color_palette', 'tableau10')
        use_gradients = params.get('use_gradients', 'false').lower() == 'true'
        use_custom_colors = params.get('use_custom_colors', 'false').lower() == 'true'
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400
        # Get custom colors if provided
        custom_colors = {}
        for i in range(32):  # Support up to 32 clusters
            color_key = f'cluster_{i}_color'
            if color_key in params:
                custom_colors[i] = params.get(color_key)
        
        # Get file from request
        filename = get_session_value(session_id, 'uploaded_file')
        if not filename:
            return jsonify({'error': 'File not found'}), 400
        
        
        # Save file to temp location
        file_path = get_file_path(session_id, filename)
        
        # Process the data
        result = connet_analysis(
            file_path,
            column_name,
            top_word_count,
            node_size_min,
            node_size_max,
            edge_width_min,
            edge_width_max,
            edge_color,
            edge_alpha,
            node_color,
            label_size,
            layout_seed,
            layout_k,
            layout_iterations,
            edge_filter_percent,
            use_concor,
            concor_n,
            concor_max_iter,
            concor_convergence,
            node_repulsion,
            group_layout_quality,
            intergroup_force,
            group_compactness,
            color_palette,
            use_gradients,
            use_custom_colors,
            custom_colors,
            label_color=label_color,
            label_font_weight=label_font_weight,
            label_background=label_background
        )
        
        # Clean up temp file
        # os.unlink(temp_file.name)
        
        return jsonify(result)
    
    except Exception as e:
        print(traceback.format_exc())
        return jsonify({'error': str(e), 'traceback': traceback.format_exc()}), 500

