# Session Management Guide

This document explains how session management works in the Text Analysis System, including how to implement session cleanup when users leave the application.

## Overview

The system uses Flask's session management to track user data and store files in session-specific directories. When a user leaves the application, it's important to clean up these resources to:

1. Free up server disk space
2. Protect user privacy by removing their data
3. Maintain system performance

## Session Structure

Each user session includes:

1. **Session Data**: Stored in Flask's session object
   - Session ID (unique identifier)
   - Uploaded file information
   - Analysis parameters
   - Results references

2. **File Storage**: Organized in session-specific directories
   - `/uploads/{session_id}/` - Contains uploaded files
   - `/results/{session_id}/` - Contains analysis results
     - `/results/{session_id}/topic_images/` - Topic visualization images
     - `/results/{session_id}/word_clouds/` - Word cloud images
     - `/results/{session_id}/word_freq/` - Word frequency data

## Session Cleanup API

The system provides an API endpoint to clear session data when a user leaves:

### Endpoint: `/api/clear-session`

**Method**: POST

**Description**: Clears all session data and removes associated files for the current session.

**Request**: No parameters required (uses the session cookie to identify the session)

**Response**:
```json
{
  "success": true,
  "message": "Session cleared successfully",
  "directories_removed": [
    "/path/to/uploads/session_id",
    "/path/to/results/session_id"
  ]
}
```

**Error Response**:
```json
{
  "success": false,
  "message": "Error message"
}
```

## Implementation in Frontend Applications

### Basic Implementation

```javascript
// Function to clear session data
async function clearSessionData() {
  try {
    const response = await fetch('/api/clear-session', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    console.log('Session cleared:', data);
    return data;
  } catch (error) {
    console.error('Error clearing session:', error);
    return { success: false, error: error.message };
  }
}

// Call this function when the user explicitly wants to clear their session
document.getElementById('clearButton').addEventListener('click', clearSessionData);
```

### Handling Page Unload

To handle cases where the user closes the browser or navigates away:

```javascript
// Show confirmation dialog when user tries to leave
window.addEventListener('beforeunload', function(e) {
  // Show confirmation dialog
  const confirmationMessage = 'You are about to leave this page. Your session data will be deleted. Continue?';
  e.returnValue = confirmationMessage;
  return confirmationMessage;
});

// If user confirms leaving, clear the session
window.addEventListener('unload', function() {
  // Using navigator.sendBeacon for reliable delivery even during page unload
  navigator.sendBeacon('/api/clear-session', JSON.stringify({}));
});
```

### React Implementation

For React applications:

```jsx
import React, { useEffect } from 'react';

function App() {
  useEffect(() => {
    // Handle page unload
    const handleBeforeUnload = (e) => {
      const confirmationMessage = 'You are about to leave this page. Your session data will be deleted. Continue?';
      e.returnValue = confirmationMessage;
      return confirmationMessage;
    };

    const handleUnload = () => {
      navigator.sendBeacon('/api/clear-session', JSON.stringify({}));
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, []);

  // Rest of your component...
}
```

## Demo Page

A demonstration page is available at `/api/session-demo` that shows how to implement session cleanup when a user leaves the application.

The demo includes:
- Checking session status
- Simulating a user leaving the page
- Explicitly clearing session data
- Handling actual page unload events

## Best Practices

1. **Always confirm before clearing**: Show a confirmation dialog before clearing session data to prevent accidental data loss.

2. **Use `navigator.sendBeacon`**: For page unload events, use `navigator.sendBeacon()` instead of `fetch()` as it's more reliable during page unload.

3. **Handle errors gracefully**: Implement proper error handling to ensure users know if their data wasn't properly cleared.

4. **Provide feedback**: Let users know when their session has been successfully cleared.

5. **Implement server-side cleanup**: In addition to client-side cleanup, implement server-side session expiration to handle cases where the client-side cleanup fails.

## Server-Side Session Expiration

The system is configured to automatically expire sessions after 1 day of inactivity. This is controlled by the `PERMANENT_SESSION_LIFETIME` setting in the Flask configuration.

You can adjust this setting in `app/config.py` if you need a different expiration period.

## Troubleshooting

If you encounter issues with session cleanup:

1. **Check browser console**: Look for any errors in the browser console when the cleanup request is made.

2. **Verify server logs**: Check the server logs for any errors related to session cleanup.

3. **Test with the demo page**: Use the `/api/session-demo` page to test session cleanup functionality.

4. **Check file permissions**: Ensure the server has permission to delete files in the upload and result directories.

5. **Verify session cookie**: Make sure the session cookie is being sent with the request by checking the request headers.
