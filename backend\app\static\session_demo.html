<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Management Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.danger {
            background-color: #f44336;
        }
        button.danger:hover {
            background-color: #d32f2f;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        #response {
            margin-top: 20px;
            white-space: pre-wrap;
        }
        .info {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Session Management Demo</h1>
    
    <div class="info">
        <p>This page demonstrates how to handle session cleanup when a user leaves the application.</p>
        <p>When you click "Simulate Leaving Page" or try to navigate away from this page, a confirmation dialog will appear.</p>
        <p>If you confirm, the session data will be cleared from the server.</p>
    </div>
    
    <div class="card">
        <h2>Session Actions</h2>
        <button id="checkSession">Check Session Status</button>
        <button id="simulateLeave" class="danger">Simulate Leaving Page</button>
        <button id="clearSession" class="danger">Clear Session Data</button>
    </div>
    
    <div id="response"></div>
    
    <script>
        // Display response in the response div
        function showResponse(data) {
            const responseElement = document.getElementById('response');
            responseElement.innerHTML = JSON.stringify(data, null, 2);
        }
        
        // Check session status
        document.getElementById('checkSession').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/upload/columns', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                showResponse(data);
            } catch (error) {
                showResponse({ error: error.message });
            }
        });
        
        // Clear session data
        async function clearSessionData() {
            try {
                const response = await fetch('/api/clear-session', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                showResponse(data);
                return data;
            } catch (error) {
                showResponse({ error: error.message });
                return { success: false, error: error.message };
            }
        }
        
        // Simulate leaving page
        document.getElementById('simulateLeave').addEventListener('click', async () => {
            if (confirm('You are about to leave this page. Your session data will be deleted. Continue?')) {
                await clearSessionData();
            }
        });
        
        // Direct clear session button
        document.getElementById('clearSession').addEventListener('click', clearSessionData);
        
        // Handle actual page unload
        window.addEventListener('beforeunload', function(e) {
            // Show confirmation dialog
            const confirmationMessage = 'You are about to leave this page. Your session data will be deleted. Continue?';
            e.returnValue = confirmationMessage;
            return confirmationMessage;
        });
        
        // If user confirms leaving, clear the session
        window.addEventListener('unload', function() {
            // Using navigator.sendBeacon for reliable delivery even during page unload
            navigator.sendBeacon('/api/clear-session', JSON.stringify({}));
        });
    </script>
</body>
</html>
