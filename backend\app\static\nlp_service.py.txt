# app/services/nlp_service.py
from konlpy.tag import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>k<PERSON>, Komoran
import spacy
import warnings
import pandas as pd
import re

from flask import current_app as app, session
import os
import json
import uuid
import traceback
import numpy as np
import matplotlib.pyplot as plt
import random
from werkzeug.utils import secure_filename
from sklearn.feature_extraction.text import CountVectorizer
from wordcloud import WordCloud
import pandas as pd
import logging

# Flask 앱 초기화 및 로깅 설정
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Ignore spaCy warnings
warnings.filterwarnings("ignore", message=r".*\[E0*")

# Try to load spaCy model
try:
    nlp_en = spacy.load('en_core_web_sm')
    SPACY_AVAILABLE = True
except:
    SPACY_AVAILABLE = False
    print("English spaCy model could not be loaded. Run 'python -m spacy download en_core_web_sm'.")

# 형태소 분석기 분류
LANGUAGE_GROUPS = {
    'korean': {
        'name': '한국어',
        'analyzers': {
            'okt': {'name': 'Open Korean Text (Okt)', 'instance': Okt()},
            'hannanum': {'name': '한나눔 (Hannanum)', 'instance': Hannanum()},
            'kkma': {'name': '꼬꼬마 (Kkma)', 'instance': Kkma()},
            'komoran': {'name': '코모란 (Komoran)', 'instance': Komoran()}
        }
    },
    'english': {
        'name': '영어',
        'analyzers': {
            'spacy': {'name': 'spaCy', 'instance': nlp_en if SPACY_AVAILABLE else None}
        }
    }
}

# 품사 태깅 설정 (영어 기본값 변경)
DEFAULT_TAGS = {
    'korean': ['Noun'],  # 한국어 기본값: 명사만 추출
    'english': ['Noun']  # 영어 기본값: 명사만 추출 (통합)
}

# Korean common POS tag mapping
KOREAN_TAG_MAPPING = {
    'okt': {
        'Noun': ['Noun'],
        'Verb': ['Verb'],
        'Adjective': ['Adjective'],
        'Adverb': ['Adverb'],
        'Determiner': ['Determiner'],
        'Exclamation': ['Exclamation'],
        'Josa': ['Josa'],
        'Eomi': ['Eomi'],
        'Suffix': ['Suffix'],
        'Prefix': ['PreEomi', 'Prefix']
    },
    'hannanum': {
        'Noun': ['N'],
        'Verb': ['P', 'XP', 'VP'],
        'Adjective': ['PA'],
        'Adverb': ['MAG'],
        'Determiner': ['MD'],
        'Exclamation': ['II'],
        'Josa': ['J'],
        'Eomi': ['E'],
        'Suffix': ['X'],
        'Prefix': []
    },
    'kkma': {
        'Noun': ['NNG', 'NNP', 'NNB', 'NNM', 'NP'],
        'Verb': ['VV', 'VXV', 'VX'],
        'Adjective': ['VA', 'VXA', 'VCP', 'VCN'],
        'Adverb': ['MAG', 'MAJ'],
        'Determiner': ['MDT', 'MDN'],
        'Exclamation': ['IC'],
        'Josa': ['JKS', 'JKC', 'JKG', 'JKO', 'JKM', 'JKI', 'JKQ', 'JX', 'JC'],
        'Eomi': ['EPH', 'EPT', 'EPP', 'EFN', 'EFQ', 'EFO', 'EFA', 'EFI', 'EFR', 'ECE', 'ECS', 'ECD', 'ETN', 'ETD'],
        'Suffix': ['XSN', 'XSV', 'XSA'],
        'Prefix': ['XPN', 'XPV']
    },
    'komoran': {
        'Noun': ['NNG', 'NNP', 'NNB'],
        'Verb': ['VV'],
        'Adjective': ['VA'],
        'Adverb': ['MAG'],
        'Determiner': ['MM'],
        'Exclamation': ['IC'],
        'Josa': ['JKS', 'JKC', 'JKG', 'JKO', 'JKB', 'JKV', 'JKQ', 'JX', 'JC'],
        'Eomi': ['EP', 'EF', 'EC', 'ETN', 'ETM'],
        'Suffix': ['XSN', 'XSV', 'XSA'],
        'Prefix': ['XPN']
    }
}

# 영어 품사 태그 (변경 없음 - 실제 spaCy 태그는 유지)
ENGLISH_POS_TAGS = {
    'NN': '명사(단수)',
    'NNS': '명사(복수)',
    'NNP': '고유명사(단수)',
    'NNPS': '고유명사(복수)',
    'JJ': '형용사',
    'JJR': '형용사(비교급)',
    'JJS': '형용사(최상급)',
    'VB': '동사(원형)',
    'VBD': '동사(과거)',
    'VBG': '동사(현재분사)',
    'VBN': '동사(과거분사)',
    'VBP': '동사(현재형)',
    'VBZ': '동사(3인칭 단수)',
    'RB': '부사',
    'RBR': '부사(비교급)',
    'RBS': '부사(최상급)'
}

FONT_PATH = os.path.join(os.path.dirname(__file__), 'malgun.ttf')  # 워드 클라우드용 폰트 경로
MAX_FEATURES = 300  # 최대 분석 단어 수

def get_language_groups():
    """Return available language groups and analyzers"""
    groups = {}
    for lang_key, lang_value in LANGUAGE_GROUPS.items():
        analyzers = {}
        for analyzer_key, analyzer_value in lang_value['analyzers'].items():
            # Disable English analyzer if spaCy is not installed
            if lang_key == 'english' and analyzer_key == 'spacy' and not SPACY_AVAILABLE:
                continue
            analyzers[analyzer_key] = analyzer_value['name']
        
        groups[lang_key] = {
            'name': lang_value['name'],
            'analyzers': analyzers
        }
    
    return groups

def get_pos_tags(language, analyzer):
    """선택한 언어와 분석기에 따른 품사 태그 목록 반환 (영어 단순화)"""
    if language == 'korean':
        # 한국어는 명사, 동사, 형용사만 제공
        pos_tags = {
            'Noun': '명사',
            'Verb': '동사',
            'Adjective': '형용사',
            # 'Adverb': '부사',
            # 'Determiner': '관형사',
            # 'Exclamation': '감탄사',
            # 'Josa': '조사',
            # 'Eomi': '어미',
            # 'Suffix': '접미사',
            # 'Prefix': '접두사'
        }
    elif language == 'english':
        # 영어는 통합된 명사, 형용사, 동사만 제공
        pos_tags = {
            'Noun': '명사',        # 통합된 명사 태그
            'JJ': '형용사',       # 형용사
            'VB': '동사(원형)'    # 동사
        }
    else:
        pos_tags = {}
    
    return pos_tags

def get_spacy_status():
    """Check if spaCy model is installed"""
    return {'installed': SPACY_AVAILABLE}

def tokenize_korean(text, analyzer_name='okt', selected_tags=None, min_word_length=2):
    """한국어 텍스트를 토큰화하고 선택된 품사만 추출"""
    # 기본값 설정
    if selected_tags is None or len(selected_tags) == 0:
        selected_tags = DEFAULT_TAGS['korean']
    
    # 텍스트 전처리 (None, NaN 처리 및 빈 문자열 체크)
    if text is None or pd.isna(text) or text.strip() == '':
        return []

    # 텍스트 타입 변환 (항상 문자열로 처리)
    text = str(text).strip()
    
    # 분석기 가져오기
    if analyzer_name not in LANGUAGE_GROUPS['korean']['analyzers']:
        analyzer_name = 'okt'  # 기본값
    
    analyzer = LANGUAGE_GROUPS['korean']['analyzers'][analyzer_name]['instance']
    
    # 선택된 품사에 해당하는 실제 태그 가져오기
    actual_tags = []
    for tag in selected_tags:
        if tag in KOREAN_TAG_MAPPING[analyzer_name]:
            actual_tags.extend(KOREAN_TAG_MAPPING[analyzer_name][tag])
    
    # 빈 태그 리스트 체크
    if not actual_tags:
        return []
    
    # 형태소 분석 진행 (각 분석기별 예외 처리 추가)
    try:
        if analyzer_name == 'okt':
            # Okt는 기본적으로 pos() 메서드 사용
            morphs = analyzer.pos(text)
        elif analyzer_name == 'hannanum':
            # 한나눔은 analyze() 후 pos() 사용 (타임아웃 설정)
            # 한나눔의 pos() 함수는 문자열을 반환하는 경우가 있어 analyze() 후 태깅
            morphs = []
            analyzed = analyzer.analyze(text)
            for ana in analyzed:
                pos_tagged = analyzer.tag_pos(ana)
                morphs.extend(pos_tagged)
        elif analyzer_name == 'kkma':
            # 꼬꼬마는 텍스트가 긴 경우 처리 문제가 있어 문장 단위로 나누어 처리
            sentences = text.split('.')  # 문장 단위로 분리
            morphs = []
            for sentence in sentences:
                if sentence.strip():  # 빈 문장은 건너뛰기
                    try:
                        # 문장별로 처리
                        sentence_morphs = analyzer.pos(sentence)
                        morphs.extend(sentence_morphs)
                    except:
                        # 개별 문장 처리 실패 시 무시하고 계속 진행
                        continue
        elif analyzer_name == 'komoran':
            # 코모란의 경우 기본 .pos() 함수 사용
            morphs = analyzer.pos(text)
        else:
            morphs = []
    except Exception as e:
        print(f"형태소 분석 오류 ({analyzer_name}): {str(e)}")
        return []
    
    # 선택된 품사만 필터링
    words = []
    for word, tag in morphs:
        if tag in actual_tags:
            if len(word) >= min_word_length:
                words.append(word)
    
    return words

def tokenize_english(text, selected_tags=None, min_word_length=2):
    """영어 텍스트를 토큰화하고 선택된 품사만 추출 (통합된 Noun 처리)"""
    if not SPACY_AVAILABLE:
        return ["[spaCy 모델이 설치되지 않음]"]
    
   # 기본값 설정
    if selected_tags is None or len(selected_tags) == 0:
        selected_tags = DEFAULT_TAGS['english']
    
    # 실제 spaCy 태그로 변환
    actual_spacy_tags = set()
    if 'Noun' in selected_tags:
        actual_spacy_tags.update(['NN', 'NNS', 'NNP', 'NNPS'])
    if 'JJ' in selected_tags:
        actual_spacy_tags.add('JJ') # 형용사 추가
        # 필요하다면 비교급, 최상급도 추가 가능: actual_spacy_tags.update(['JJ', 'JJR', 'JJS'])
    if 'VB' in selected_tags:
        actual_spacy_tags.add('VB') # 동사 원형 추가
        # 필요하다면 다른 동사 형태도 추가 가능: actual_spacy_tags.update(['VB', 'VBD', 'VBG', 'VBN', 'VBP', 'VBZ'])


    # 선택된 태그가 없으면 빈 리스트 반환
    if not actual_spacy_tags:
        return []

    doc = nlp_en(text)
    words = []
    for token in doc:
        if token.tag_ in actual_spacy_tags: # 변환된 실제 태그 사용
            if len(token.lemma_) >= min_word_length:
                # 소문자 변환 및 불용어 제외
                lemma = token.lemma_.lower()
                if not token.is_stop and lemma.isalpha():
                    words.append(lemma)
    
    return words

def remove_special_characters_and_numbers(text):
    """
    영어 알파벳(소문자 및 대문자), 밑줄(_), 한국어, 일본어 (히라가나, 가타카나), 중국어 문자를 제외한 모든 문자와 숫자를 제거
    """
    # 영어 알파벳 및 밑줄, 공백, 한국어, 일본어, 중국어 문자를 제외한 모든 것을 제거하는 패턴
    pattern = r'[^\sa-zA-Z_\uAC00-\uD7A3\u3040-\u309F\u30A0-\u30FF\u31F0-\u31FF\u4E00-\u9FFF]'
    # 정규 표현식을 사용하여 패턴에 일치하지 않는 모든 문자를 제거
    result = re.sub(pattern, '', text)
    return result


def read_file(file_path):
    """파일을 읽어서 데이터프레임으로 반환하는 함수"""
    try:
        # 파일 확장자 확인
        if file_path.lower().endswith(('.xlsx', '.xls')):
            # 엑셀 파일인 경우
            data = pd.read_excel(file_path)
        else:
            # CSV 파일 시도 (다양한 인코딩 방식으로)
            try:
                # UTF-8 인코딩으로 시도
                data = pd.read_csv(file_path, encoding='utf-8')
            except:
                try:
                    # CP949 인코딩으로 시도
                    data = pd.read_csv(file_path, encoding='cp949')
                except:
                    try:
                        # 파이썬 엔진으로 구분자 자동 감지 시도
                        data = pd.read_csv(file_path, encoding='utf-8', engine='python', sep=None)
                    except:
                        # 마지막으로 엑셀 파일로 시도
                        data = pd.read_excel(file_path)
        
        return data
    except Exception as e:
        # logger.error(f"파일 읽기 오류: {e}")
        raise Exception("파일을 읽을 수 없습니다. CSV 또는 Excel 파일 형식이 맞는지 확인해주세요.")

def create_wordcloud_mask(shape, width=1000, height=1000):
    """워드클라우드 마스크 생성"""
    if shape == 'circle':
        x, y = np.ogrid[:width, :height]
        center = width/2, height/2
        radius = min(width, height)/2 * 0.9  # 90% of the minimum dimension
        
        # 원 바깥 영역을 선택 (True)
        circle_mask = ((x - center[0])**2 + (y - center[1])**2 > radius**2)
        
        # 마스크 반전: 워드클라우드는 값이 0인 영역에 단어를 그립니다
        # 따라서 원 안쪽을 0으로, 바깥쪽을 255로 설정
        return 255 * circle_mask.astype(int)
    return None

def get_color_function(color_theme):
    """워드클라우드 색상 함수 생성"""
    colormap = plt.cm.get_cmap(color_theme)
    
    def color_func(*args, **kwargs):
        return tuple(int(x * 255) for x in colormap(random.random()))
    
    return color_func

def get_file_path(session_id, filename):
    """세션 ID와 파일명을 기반으로 파일 경로 생성"""
    return os.path.join(app.config['UPLOAD_FOLDER'], session_id, filename)



def analyze_frequency(file_path, column_name, selection_type='top_n', max_words=50, cloud_shape='rectangle', cloud_color='viridis', selected_words=None):
    """단어 빈도 분석 및 워드 클라우드 생성 함수"""
    try:
        # 파일 읽기
        data = read_file(file_path)
        
        # 지정한 컬럼이 있는지 확인
        if column_name not in data.columns:
            return {"error": f"'{column_name}' 컬럼을 찾을 수 없습니다."}
        
        # 결과 파일용 고유 파일명 생성
        filename = f"frequency_{uuid.uuid4().hex[:8]}"
        
        # 비문자열 값 처리 (NaN을 빈 문자열로 변환하고 모든 값을 문자열로 변환)
        data[column_name] = data[column_name].fillna('').astype(str)
        
        # 단어 빈도 분석
        cv = CountVectorizer(max_features=MAX_FEATURES, ngram_range=(1,1))
        tdm = cv.fit_transform(data[column_name])

        # 단어와 빈도를 데이터프레임으로 변환
        word_count_df = pd.DataFrame({
            '단어': cv.get_feature_names_out(), 
            '빈도': tdm.sum(axis=0).tolist()[0]
        })

        # 빈도수 기준으로 내림차순 정렬
        word_count_df = word_count_df.sort_values('빈도', ascending=False)
        word_count_df = word_count_df.reset_index(drop=True)

        # 전체 단어 빈도수 합계 계산
        total_word_count = word_count_df['빈도'].sum()

        # 각 단어의 빈도 비율(%) 계산
        word_count_df['비율(%)'] = (word_count_df['빈도'] / total_word_count) * 100
        
        # 결과를 CSV 파일로 저장
        csv_filename = f"{filename}_frequency.csv"
        csv_path = os.path.join(app.config['RESULT_FOLDER'], csv_filename)
        word_count_df.to_csv(csv_path, encoding='cp949', index=False)
        
        # 상위 200개 단어 목록 생성 (항상 단어 데이터 제공)
        top_words = word_count_df.head(200).apply(
            lambda row: {'word': row['단어'], 'frequency': float(row['빈도'])}, 
            axis=1
        ).tolist()
        
        # 수동 선택 모드인 경우 단어 목록만 반환
        if selection_type == 'manual' and selected_words is None:
            return {
                "success": True,
                "output_file": csv_filename,
                "word_data": top_words
            }
            
        # 워드 클라우드 생성
        wordcloud_filename = f"{filename}_wordcloud.png"
        wordcloud_path = os.path.join(app.config['RESULT_FOLDER'], wordcloud_filename)
        
        # 워드 클라우드용 단어-빈도 사전 생성
        if selection_type == 'manual' and selected_words:
            # 수동 선택 모드: 선택된 단어만 포함
            word_list = json.loads(selected_words)
            
            # 선택된 단어의 실제 빈도수 가져오기
            count_dict = {}
            for word in word_list:
                word_row = word_count_df[word_count_df['단어'] == word]
                if not word_row.empty:
                    count_dict[word] = word_row['빈도'].values[0]
                else:
                    count_dict[word] = 1  # 기본값
        else:
            # 자동 선택 모드: 상위 N개 단어 사용
            top_df = word_count_df.head(int(max_words))
            count_dict = dict(zip(top_df['단어'], top_df['빈도']))
        
        try:
            # 워드 클라우드 형태와 색상 설정
            mask = create_wordcloud_mask(cloud_shape)
            color_func = get_color_function(cloud_color)
            
            # 워드 클라우드 생성 파라미터 (폰트 경로가 None이면 기본 폰트 사용)
            wordcloud_params = {
                'background_color': 'white',
                'max_words': len(count_dict),
                'width': 1000,
                'height': 1000,
                'mask': mask,
                'color_func': color_func
            }
            
            # 폰트 경로가 있는 경우만 파라미터에 추가
            if FONT_PATH:
                wordcloud_params['font_path'] = FONT_PATH
            
            # 워드 클라우드 생성
            wc = WordCloud(**wordcloud_params)
            cloud = wc.fit_words(count_dict)
            cloud.to_file(wordcloud_path)
            
            # 파일이 실제로 생성되었는지 확인
            if not os.path.exists(wordcloud_path) or os.path.getsize(wordcloud_path) == 0:
                raise Exception("워드클라우드 파일 생성에 실패했습니다.")
                
            has_wordcloud = True
        except Exception as e:
            logger.error(f"워드 클라우드 생성 오류: {e}")
            has_wordcloud = False
        
        # 결과 반환 (항상 단어 데이터 포함)
        return {
            "success": True,
            "output_file": csv_filename,
            "wordcloud_file": wordcloud_filename if has_wordcloud else None,
            "word_data": top_words
        }
        
    except Exception as e:
        logger.error(f"단어 빈도 분석 오류: {traceback.format_exc()}")
        return {"error": str(e)}


def create_wordcloud_from_words(word_list, cloud_shape, cloud_color):
    """단어 목록으로 워드클라우드 생성"""
    try:
        if not word_list or len(word_list) < 10:
            return {"error": "최소 10개 이상의 단어를 선택해주세요."}
        
        # 결과 파일용 고유 파일명 생성
        filename = f"wordcloud_{uuid.uuid4().hex[:8]}"
        wordcloud_filename = f"{filename}.png"
        wordcloud_path = os.path.join(app.config['RESULT_FOLDER'], wordcloud_filename)
        
        # 마스크와 색상 함수 생성
        mask = create_wordcloud_mask(cloud_shape)
        color_func = get_color_function(cloud_color)
        
        # 단어-빈도 사전 생성 (모든 단어에 동일한 빈도 할당)
        count_dict = {word: 1 for word in word_list}
        
        # 워드 클라우드 생성 파라미터 (폰트 경로가 None이면 기본 폰트 사용)
        wordcloud_params = {
            'background_color': 'white',
            'max_words': len(count_dict),
            'width': 1000,
            'height': 1000,
            'mask': mask,
            'color_func': color_func
        }
        
        # 폰트 경로가 있는 경우만 파라미터에 추가
        if FONT_PATH:
            wordcloud_params['font_path'] = FONT_PATH
        
        # 워드 클라우드 생성
        try:
            wc = WordCloud(**wordcloud_params)
            cloud = wc.fit_words(count_dict)
            cloud.to_file(wordcloud_path)
            
            # 파일이 실제로 생성되었는지 확인
            if not os.path.exists(wordcloud_path) or os.path.getsize(wordcloud_path) == 0:
                return {"error": "워드클라우드 생성에 실패했습니다. 리소스 접근 오류."}
                
            return {
                "success": True,
                "wordcloud_file": wordcloud_filename
            }
        except Exception as inner_error:
            logger.error(f"워드클라우드 생성 중 내부 오류: {inner_error}")
            return {"error": f"워드클라우드 생성 오류: {str(inner_error)}"}
    except Exception as e:
        logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
        return {"error": f"워드클라우드 처리 오류: {str(e)}"}