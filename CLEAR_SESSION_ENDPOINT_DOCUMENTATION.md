# Clear Session Endpoint Documentation

## Overview

The `/api/clear-session` endpoint has been enhanced to support both clearing specific sessions and clearing all sessions at once. This provides flexible session management capabilities for both user-initiated cleanup and administrative maintenance.

## Endpoint Details

### Base Endpoint
```
POST /api/clear-session
```

## Usage Modes

### 1. Clear Specific Session

Clears a single session by providing its session ID.

**Parameters:**
- `session_id` (string, required): The specific session ID to clear

**Example Request:**
```bash
curl -X POST "http://localhost:7000/api/clear-session?session_id=abc123def4"
```

**Success Response:**
```json
{
  "success": true,
  "message": "Session abc123def4 cleared successfully",
  "session_id": "abc123def4",
  "directories_removed": [
    "/path/to/uploads/abc123def4",
    "/path/to/results/abc123def4"
  ]
}
```

**Error Response (Session Not Found):**
```json
{
  "success": false,
  "message": "Session with ID abc123def4 not found"
}
```

### 2. Clear All Sessions

Clears all sessions and their associated files.

**Parameters:**
- `clear_all` (boolean, required): Set to `true` to clear all sessions

**Example Request:**
```bash
curl -X POST "http://localhost:7000/api/clear-session?clear_all=true"
```

**Success Response:**
```json
{
  "success": true,
  "message": "Cleared 5 sessions successfully",
  "cleared_sessions": ["abc123def4", "xyz789ghi0", "def456jkl1"],
  "directories_removed": [
    "/path/to/uploads/abc123def4",
    "/path/to/results/abc123def4",
    "/path/to/uploads/xyz789ghi0",
    "/path/to/results/xyz789ghi0"
  ],
  "total_sessions_cleared": 5,
  "total_directories_removed": 10
}
```

**Response with Warnings:**
```json
{
  "success": true,
  "message": "Cleared 3 sessions successfully (with 2 warnings)",
  "cleared_sessions": ["abc123def4", "xyz789ghi0"],
  "directories_removed": ["/path/to/uploads/abc123def4"],
  "total_sessions_cleared": 3,
  "total_directories_removed": 1,
  "warnings": [
    "Error removing upload directory for xyz789ghi0: Permission denied",
    "Error removing result directory for def456jkl1: Directory not found"
  ]
}
```

### 3. Error Cases

**No Parameters Provided:**
```bash
curl -X POST "http://localhost:7000/api/clear-session"
```

**Response:**
```json
{
  "success": false,
  "message": "No session ID provided. Use session_id parameter for specific session or clear_all=true for all sessions."
}
```

## Additional Session Management Endpoints

### List All Sessions

Get information about all active sessions.

**Endpoint:**
```
GET /api/sessions
```

**Example Request:**
```bash
curl -X GET "http://localhost:7000/api/sessions"
```

**Response:**
```json
{
  "success": true,
  "sessions": [
    {
      "session_id": "abc123def4",
      "created_at": 1703123456.789,
      "last_accessed": 1703123500.123,
      "has_upload_dir": true,
      "has_result_dir": true,
      "upload_size_bytes": 1048576,
      "result_size_bytes": 2097152,
      "total_size_bytes": 3145728,
      "data_keys": ["uploaded_file", "options"]
    }
  ],
  "total_sessions": 1,
  "total_size_bytes": 3145728,
  "total_size_mb": 3.0
}
```

## What Gets Cleared

When clearing sessions, the following items are removed:

### For Each Session:
1. **Session JSON file**: `/sessions/{session_id}.json`
2. **Upload directory**: `/uploads/{session_id}/` (and all contents)
3. **Result directory**: `/results/{session_id}/` (and all contents)
   - Topic model files
   - Word cloud images
   - Word frequency data
   - Analysis results

### Additional Cleanup (Clear All Mode):
- **Orphaned directories**: Directories without corresponding session files
- **Validation**: Ensures session directories match valid session ID format

## Implementation Details

### Session ID Validation
- Session IDs must be 10-character hexadecimal strings
- Only directories matching this pattern are considered for cleanup
- Invalid directories are ignored to prevent accidental deletion

### Error Handling
- **Graceful degradation**: Continues processing even if individual operations fail
- **Detailed logging**: Errors are logged for debugging
- **Warning collection**: Non-critical errors are returned as warnings
- **Atomic operations**: Each session is processed independently

### Safety Features
- **Existence checks**: Verifies session exists before attempting deletion
- **Permission handling**: Gracefully handles permission errors
- **Path validation**: Ensures only session-related paths are affected
- **Rollback protection**: Failed operations don't affect other sessions

## JavaScript Integration

### Clear Specific Session
```javascript
async function clearSession(sessionId) {
  try {
    const response = await fetch(`/api/clear-session?session_id=${sessionId}`, {
      method: 'POST',
      credentials: 'include'
    });
    const data = await response.json();
    
    if (data.success) {
      console.log(`Session ${sessionId} cleared successfully`);
      return data;
    } else {
      console.error('Failed to clear session:', data.message);
      return null;
    }
  } catch (error) {
    console.error('Error clearing session:', error);
    return null;
  }
}
```

### Clear All Sessions
```javascript
async function clearAllSessions() {
  try {
    const response = await fetch('/api/clear-session?clear_all=true', {
      method: 'POST',
      credentials: 'include'
    });
    const data = await response.json();
    
    if (data.success) {
      console.log(`Cleared ${data.total_sessions_cleared} sessions`);
      if (data.warnings) {
        console.warn('Warnings:', data.warnings);
      }
      return data;
    } else {
      console.error('Failed to clear all sessions:', data.message);
      return null;
    }
  } catch (error) {
    console.error('Error clearing all sessions:', error);
    return null;
  }
}
```

### List Sessions
```javascript
async function listSessions() {
  try {
    const response = await fetch('/api/sessions', {
      credentials: 'include'
    });
    const data = await response.json();
    
    if (data.success) {
      console.log(`Found ${data.total_sessions} sessions (${data.total_size_mb} MB)`);
      return data.sessions;
    } else {
      console.error('Failed to list sessions:', data.message);
      return [];
    }
  } catch (error) {
    console.error('Error listing sessions:', error);
    return [];
  }
}
```

## Testing

Use the provided test script to verify functionality:

```bash
python test_clear_session.py
```

The test script covers:
- Listing sessions
- Creating test sessions
- Clearing specific sessions
- Clearing all sessions
- Error case handling
- Endpoint documentation

## Security Considerations

### Access Control
- No authentication required (session-based application)
- Consider adding admin-only access for clear-all functionality
- Rate limiting recommended for production use

### Data Protection
- **Irreversible operation**: Cleared data cannot be recovered
- **User confirmation**: Implement confirmation dialogs in frontend
- **Audit logging**: Consider logging session cleanup operations

### Production Recommendations
- **Backup strategy**: Regular backups before bulk cleanup
- **Monitoring**: Track session cleanup frequency and patterns
- **Alerts**: Monitor for unusual cleanup activity
- **Maintenance windows**: Schedule bulk cleanup during low-usage periods
