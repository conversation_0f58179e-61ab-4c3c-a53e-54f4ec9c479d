# app/blueprints/analysis/tfidf_routes.py
import json
import logging
import os
import traceback

from flask import Blueprint, jsonify, request, send_file
from werkzeug.utils import secure_filename

from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import (TFIDFAnalyzer, analyze_frequency, WordCloudGenerator,
                                     create_topic_network,
                                     create_updated_pyldavis, get_file_path,
                                     get_lda_model, get_updated_topics,
                                     read_file, run_lda_analysis,
                                     run_lda_analysis_with_edits,
                                     update_topic_visualizations)
from ...services.session_service import get_or_create_session_id
from . import tfidf_bp

# Setup logging
logger = logging.getLogger(__name__)


@tfidf_bp.route("/get_word_data", methods=["POST", "OPTIONS"])
def get_word_data():
    """Get word list data for manual selection mode"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()

        # File validation
        if "file" not in request.files:
            return jsonify({"error": "파일이 전송되지 않았습니다"}), 400

        file = request.files["file"]
        if file.filename == "":
            return jsonify({"error": "파일을 선택해주세요"}), 400

        column_name = request.form.get("column_name")
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        # Save file
        filename = get_session_value(session_id, "uploaded_file")
        file_path = get_file_path(session_id, filename)
        file.save(file_path)

        # Analyze using TFIDFAnalyzer
        analyzer = TFIDFAnalyzer()
        result = analyzer.analyze_tfidf(
            file_path, column_name, session_id, selection_type="manual"
        )

        if "error" in result:
            return jsonify(result), 400

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                "word_data": result["word_data"],
            }
        )

    except Exception as e:
        logger.error(f"단어 데이터 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@tfidf_bp.route("/analyze", methods=["POST", "OPTIONS"])
def analyze():
    """Main TF-IDF analysis endpoint"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()

        # File validation
        filename = get_session_value(session_id, "uploaded_file")
        if not filename:
            return jsonify({"error": "파일을 선택해주세요"}), 400

        column_name = request.form.get("column_name")
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        # Get file path
        file_path = get_file_path(session_id, filename)
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # Additional parameters
        selection_type = request.form.get("selection_type", "top_n")
        max_words = request.form.get("max_words", "50")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")
        selected_words = request.form.get("selected_words", None)

        # Run analysis
        analyzer = TFIDFAnalyzer()
        result = analyzer.analyze_tfidf(
            file_path,
            column_name,
            session_id,
            selection_type,
            max_words,
            cloud_shape,
            cloud_color,
            selected_words,
        )

        if "error" in result:
            return jsonify(result), 400

        return jsonify({"success": True, "session_id": session_id, **result})

    except Exception as e:
        logger.error(f"분석 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@tfidf_bp.route("/generate_wordcloud", methods=["POST", "OPTIONS"])
def generate_wordcloud():
    """Generate wordcloud from selected words"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()

        selected_words = request.form.get("selected_words", "[]")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")

        word_list = json.loads(selected_words)
        if not word_list or len(word_list) < 10:
            return jsonify({"error": "최소 10개 이상의 단어를 선택해주세요."}), 400

        # Generate wordcloud
        wordcloud_generator = WordCloudGenerator()
        result = wordcloud_generator.generate_from_words(
            word_list, session_id, cloud_shape, cloud_color
        )

        if "error" in result:
            return jsonify(result), 400

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                "wordcloud_file": result["wordcloud_file"],
            }
        )

    except Exception as e:
        logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@tfidf_bp.route("/edit_words", methods=["POST", "OPTIONS"])
def edit_words():
    """Edit words and regenerate wordcloud"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()

        edited_words = request.form.get("edited_words", "[]") # list of dictions [{"new":"new_word", "original":"old_word"}]
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")

        word_list = json.loads(edited_words)
        if not word_list or len(word_list) < 10:
            return jsonify({"error": "최소 10개 이상의 단어가 필요합니다."}), 400

        # Generate wordcloud with edited words
        wordcloud_generator = WordCloudGenerator()
        result = wordcloud_generator.generate_from_words(
            word_list, session_id, cloud_shape, cloud_color, prefix="edited"
        )

        if "error" in result:
            return jsonify(result), 400

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                "wordcloud_file": result["wordcloud_file"],
            }
        )

    except Exception as e:
        logger.error(f"단어 편집 및 워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500
