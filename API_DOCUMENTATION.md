# API Documentation

## Base URL

```
http://localhost:7000/api
```

When deployed, replace with your deployment URL.

## Authentication

The API uses session-based authentication. Session cookies are automatically managed by the browser when making requests.

## File Upload and Management

### Upload File

**Endpoint:** `POST /upload`

Uploads a CSV or Excel file to the server and creates a session.

**Request:**
- Content-Type: `multipart/form-data`
- Form Field: `file` (CSV or Excel file)

**Response:**
```json
{
  "success": true,
  "filename": "uploaded_file.xlsx",
  "columns": ["Column1", "Column2", "Column3"]
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "파일이 전송되지 않았습니다."
}
```

**Example (JavaScript):**
```javascript
const formData = new FormData();
formData.append("file", selectedFile);

fetch("http://localhost:7000/api/upload", {
  method: "POST",
  body: formData,
  credentials: "include"
})
  .then(res => res.json())
  .then(data => console.log(data));
```

### Get Columns

**Endpoint:** `GET /columns`

Returns the columns from the uploaded file.

**Response:**
```json
{
  "success": true,
  "columns": ["Column1", "Column2", "Column3"]
}
```

**Error Response:**
```json
{
  "error": "파일이 업로드되지 않았습니다.",
  "success": false
}
```

## Text Processing

### Process File

**Endpoint:** `POST /process`

Processes the uploaded file with NLP analysis.

**Request:**
- Content-Type: `application/x-www-form-urlencoded`
- Parameters:
  - `column_name`: Name of the column to analyze
  - `language`: Language of the text (`korean` or `english`)
  - `analyzer`: Analyzer to use (`okt`, `hannanum`, `kkma`, `komoran` for Korean, `spacy` for English)
  - `pos_tags`: List of POS tags to include
  - `min_word_length`: Minimum word length (default: 2)
  - `custom_filename`: Custom filename for the output (optional)

**Response:**
```json
{
  "task_id": "84e7cfe9-2acc-49f3-989c-97ed9d34c335",
  "status": "processing",
  "message": "Your file is being processed"
}
```

### Check Processing Progress

**Endpoint:** `GET /progress/<task_id>`

Returns the progress of a processing task.

**Response:**
```json
{
  "task_id": "84e7cfe9-2acc-49f3-989c-97ed9d34c335",
  "progress": 75
}
```

### Get Processing Result

**Endpoint:** `GET /result/<task_id>`

Returns the result of a completed processing task.

**Response:**
```json
{
  "success": true,
  "task_id": "84e7cfe9-2acc-49f3-989c-97ed9d34c335",
  "status": "completed",
  "download_url": "/api/download/processed_file.xlsx",
  "filename": "processed_file.xlsx"
}
```

### Download Processed File

**Endpoint:** `GET /download/<filename>`

Downloads a processed file.

**Response:** The file as an attachment.

## Language and NLP Configuration

### Get Language Groups

**Endpoint:** `GET /language-groups`

Returns available language groups and analyzers.

**Response:**
```json
{
  "korean": {
    "name": "한국어",
    "analyzers": {
      "okt": {"name": "Open Korean Text (Okt)"},
      "hannanum": {"name": "한나눔 (Hannanum)"},
      "kkma": {"name": "꼬꼬마 (Kkma)"},
      "komoran": {"name": "코모란 (Komoran)"}
    }
  },
  "english": {
    "name": "영어",
    "analyzers": {
      "spacy": {"name": "spaCy"}
    }
  }
}
```

### Get POS Tags

**Endpoint:** `GET /pos-tags/<language>/<analyzer>`

Returns available POS tags for the selected language and analyzer.

**Response:**
```json
{
  "Noun": "명사",
  "Verb": "동사",
  "Adjective": "형용사",
  "Adverb": "부사",
  "Determiner": "관형사",
  "Exclamation": "감탄사",
  "Josa": "조사",
  "Eomi": "어미",
  "PreEomi": "선어미",
  "Conjunction": "접속사"
}
```

### Check spaCy Status

**Endpoint:** `GET /spacy-status`

Checks if the spaCy model is installed.

**Response:**
```json
{
  "installed": true,
  "model": "en_core_web_sm"
}
```

## Analysis

### Word Frequency Analysis

**Endpoint:** `POST /analyse/analyze`

Performs word frequency analysis on the text data.

**Request:**
- Content-Type: `application/x-www-form-urlencoded`
- Parameters:
  - `column_name`: Name of the column to analyze
  - `selection_type`: Selection type (`top_n` or `manual`)
  - `max_words`: Maximum number of words (default: 50)
  - `cloud_shape`: Word cloud shape (`rectangle`, `circle`, etc.)
  - `cloud_color`: Word cloud color scheme (`viridis`, `plasma`, etc.)
  - `selected_words`: JSON string of selected words (optional)

**Response:**
```json
{
  "success": true,
  "output_file": "frequency_12345678.csv",
  "wordcloud_file": "wordcloud_12345678.png",
  "word_data": [
    {"word": "example", "frequency": 42, "percentage": 5.2},
    {"word": "analysis", "frequency": 38, "percentage": 4.7}
  ]
}
```

### Generate Word Cloud

**Endpoint:** `POST /analyse/wordcloud`

Generates a word cloud from selected words.

**Request:**
- Content-Type: `application/x-www-form-urlencoded`
- Parameters:
  - `selected_words`: JSON string of selected words
  - `cloud_shape`: Word cloud shape
  - `cloud_color`: Word cloud color scheme

**Response:**
```json
{
  "success": true,
  "wordcloud_file": "wordcloud_12345678.png"
}
```

### Edit Words

**Endpoint:** `POST /analyse/edit_words`

Edits words and regenerates the word cloud.

**Request:**
- Content-Type: `application/x-www-form-urlencoded`
- Parameters:
  - `edited_words`: JSON string of edited words
  - `cloud_shape`: Word cloud shape
  - `cloud_color`: Word cloud color scheme

**Response:**
```json
{
  "success": true,
  "wordcloud_file": "wordcloud_12345678.png"
}
```

### LDA Topic Modeling

**Endpoint:** `POST /analyse/process`

Performs LDA topic modeling on the text data.

**Request:**
- Content-Type: `application/json`
- Body:
```json
{
  "text_column": "Column1",
  "min_topic": 3,
  "max_topic": 10,
  "no_below": 5,
  "no_above": 0.2,
  "network_style": "academic",
  "manual_topic_number": null,
  "chart_style": "default"
}
```

**Response:**
```json
{
  "optimal_topics": 5,
  "perplexity_scores": [...],
  "coherence_scores": [...],
  "perplexity_plot": "data:image/png;base64,...",
  "coherence_plot": "data:image/png;base64,...",
  "topics": [
    {
      "id": 0,
      "words": [{"word": "example", "weight": 0.042}, ...]
    },
    ...
  ],
  "network_img_path": "/api/results/topic_network_12345678.png",
  "wordcloud_img_path": "/api/results/wordcloud_12345678.png",
  "topic_images": [
    {"id": 0, "path": "/api/results/topic_0_default.png"},
    ...
  ],
  "pyldavis_html": "<div>...</div>",
  "csv_path": "/api/results/lda_topics_12345678.csv"
}
```

### Edit Keywords

**Endpoint:** `POST /analyse/edit_keywords`

Edits keywords in the LDA topic model.

**Request:**
- Content-Type: `application/json`
- Body:
```json
{
  "topic_id": 0,
  "edited_words": [
    {"original": "example", "new": "sample"},
    ...
  ],
  "removed_words": ["word1", "word2"],
  "chart_style": "default",
  "network_style": "academic"
}
```

**Response:**
```json
{
  "topics": [...],
  "network_img_path": "/api/results/topic_network_12345678.png",
  "wordcloud_img_path": "/api/results/wordcloud_12345678.png",
  "topic_images": [...],
  "pyldavis_html": "<div>...</div>"
}
```

### Update Chart Style

**Endpoint:** `POST /analyse/update_chart_style`

Updates the chart style for topic visualizations.

**Request:**
- Content-Type: `application/json`
- Body:
```json
{
  "chart_style": "dark"
}
```

**Response:**
```json
{
  "success": true,
  "topic_images": [...],
  "chart_style": "dark"
}
```

## File Access

### Serve Results File

**Endpoint:** `GET /results/<subpath>`

Serves a file from the results directory.

**Response:** The requested file.

## Error Handling

All API endpoints return appropriate HTTP status codes:

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request parameters
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

Error responses include a JSON object with an `error` field describing the error.
