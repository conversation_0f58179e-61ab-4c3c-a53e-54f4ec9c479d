# Swagger API Documentation Updates

## Overview

The Swagger API documentation has been updated to include the enhanced session management endpoints. This provides comprehensive API documentation for both developers and users of the analysis application.

## New Endpoints Added

### 1. List All Sessions - `GET /sessions`

**Purpose**: Retrieve information about all active sessions with metadata and statistics.

**Response Schema**:
```json
{
  "success": true,
  "sessions": [
    {
      "session_id": "abc123def4",
      "created_at": 1703123456.789,
      "last_accessed": 1703123500.123,
      "has_upload_dir": true,
      "has_result_dir": true,
      "upload_size_bytes": 1048576,
      "result_size_bytes": 2097152,
      "total_size_bytes": 3145728,
      "data_keys": ["uploaded_file", "options"]
    }
  ],
  "total_sessions": 5,
  "total_size_bytes": 15728640,
  "total_size_mb": 15.0
}
```

**Features**:
- Lists all active sessions with detailed metadata
- Shows file sizes and directory status
- Provides total statistics across all sessions
- Includes error handling for server issues

### 2. Enhanced Clear Session - `POST /clear-session`

**Purpose**: Clear session data with support for both specific sessions and bulk clearing.

**Parameters**:
- `session_id` (query, optional): Specific session ID to clear
- `clear_all` (query, optional): Set to `true` to clear all sessions

**Two Response Types**:

#### Specific Session Clearing:
```json
{
  "success": true,
  "message": "Session abc123def4 cleared successfully",
  "session_id": "abc123def4",
  "directories_removed": [
    "/path/to/uploads/abc123def4",
    "/path/to/results/abc123def4"
  ]
}
```

#### Bulk Session Clearing:
```json
{
  "success": true,
  "message": "Cleared 5 sessions successfully",
  "cleared_sessions": ["abc123def4", "xyz789ghi0", "def456jkl1"],
  "directories_removed": [
    "/path/to/uploads/abc123def4",
    "/path/to/results/abc123def4"
  ],
  "total_sessions_cleared": 5,
  "total_directories_removed": 10,
  "warnings": [
    "Error removing upload directory for xyz789ghi0: Permission denied"
  ]
}
```

## Documentation Features

### Comprehensive Error Handling

**400 Bad Request**:
- Missing required parameters
- Invalid parameter combinations

**404 Not Found**:
- Session ID not found
- No sessions available

**500 Internal Server Error**:
- Server-side processing errors
- File system access issues

### Parameter Documentation

**Query Parameters**:
- Clear parameter descriptions
- Required/optional indicators
- Example values
- Usage guidelines

### Response Schema Validation

**oneOf Schema**:
- Supports multiple response formats
- Validates different response types
- Provides clear examples for each case

### Tags and Organization

**Session Management Tag**:
- Groups related endpoints together
- Provides logical API organization
- Improves navigation in Swagger UI

## Usage Examples

### Using Swagger UI

1. **Access Documentation**:
   ```
   http://localhost:7000/api/docs
   ```

2. **Test List Sessions**:
   - Navigate to "Session Management" section
   - Click on "GET /sessions"
   - Click "Try it out" and "Execute"

3. **Test Clear Specific Session**:
   - Navigate to "POST /clear-session"
   - Add `session_id` parameter
   - Click "Try it out" and "Execute"

4. **Test Clear All Sessions**:
   - Navigate to "POST /clear-session"
   - Set `clear_all` parameter to `true`
   - Click "Try it out" and "Execute"

### cURL Examples

**List all sessions**:
```bash
curl -X GET "http://localhost:7000/api/sessions"
```

**Clear specific session**:
```bash
curl -X POST "http://localhost:7000/api/clear-session?session_id=abc123def4"
```

**Clear all sessions**:
```bash
curl -X POST "http://localhost:7000/api/clear-session?clear_all=true"
```

## API Client Generation

The updated Swagger documentation supports:

### Code Generation
- **JavaScript/TypeScript**: Generate client libraries
- **Python**: Generate SDK with proper typing
- **Java**: Generate REST client classes
- **C#**: Generate API client code

### Tools Supported
- **Swagger Codegen**: Generate clients in multiple languages
- **OpenAPI Generator**: Modern code generation tool
- **Postman**: Import collection for testing
- **Insomnia**: Import workspace for API testing

## Validation and Testing

### Schema Validation
- **Request validation**: Parameters are properly typed
- **Response validation**: Responses match documented schemas
- **Error handling**: All error cases are documented

### Interactive Testing
- **Swagger UI**: Built-in testing interface
- **Parameter validation**: Real-time parameter checking
- **Response inspection**: View actual API responses

## Integration Benefits

### Developer Experience
- **Clear documentation**: Comprehensive endpoint descriptions
- **Interactive testing**: Test APIs directly from documentation
- **Code examples**: Ready-to-use code snippets
- **Error guidance**: Clear error messages and solutions

### API Consistency
- **Standardized responses**: Consistent response formats
- **Error handling**: Uniform error response structure
- **Parameter naming**: Consistent parameter conventions
- **Status codes**: Proper HTTP status code usage

## Maintenance

### Documentation Updates
- **Version control**: Track documentation changes
- **Automated validation**: Ensure schema accuracy
- **Regular reviews**: Keep documentation current
- **User feedback**: Incorporate developer suggestions

### Quality Assurance
- **Schema validation**: Automated schema checking
- **Example testing**: Verify example responses
- **Link validation**: Ensure all references work
- **Accessibility**: Documentation accessibility compliance

## Future Enhancements

### Planned Additions
- **Authentication documentation**: When auth is implemented
- **Rate limiting**: Document API limits
- **Webhooks**: Document callback endpoints
- **Batch operations**: Document bulk API operations

### Advanced Features
- **API versioning**: Support multiple API versions
- **Custom schemas**: Domain-specific data types
- **Extended examples**: More comprehensive use cases
- **Performance metrics**: Document expected response times
