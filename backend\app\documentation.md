# 📁 File Upload & Column Selection API (Flask)

## 📌 Base URL
```
http://localhost:5000/api
```

---

## 1. **Upload File**

### **POST** `/upload`

Upload a CSV or Excel file to the server. This creates a session and associates the uploaded file with that session.

### 🔸 Request
- **Content-Type**: `multipart/form-data`
- **Form Field**: `file`
- **Accepted Files**: `.csv`, `.xlsx`

### ✅ Example Response:
```json
{
  "success": true,
  "filename": "uploaded_file.xlsx",
  "columns": ["Name", "Email", "Age"]
}
```

### ❌ Error Response:
```json
{
  "success": false,
  "error": "파일이 전송되지 않았습니다."
}
```

---

### 🧪 cURL Example:
```bash
curl -X POST http://localhost:5000/api/upload \
  -F "file=@/path/to/your/data.xlsx" \
  -c cookies.txt -b cookies.txt
```

### ⚛️ JavaScript (React / Fetch):
```js
const formData = new FormData();
formData.append("file", selectedFile);

fetch("http://localhost:5000/api/upload", {
  method: "POST",
  body: formData,
  credentials: "include", // important to maintain session
})
  .then(res => res.json())
  .then(data => console.log(data));
```

---

## 2. **Select Columns**

### **POST** `/select-columns`

After uploading a file, use this to select the relevant columns. The file is retrieved automatically from the session.

### 🔸 Request
- **Content-Type**: `application/json`
- **Body**:
```json
{
  "columns": ["Name", "Email"]
}
```

### ✅ Example Response:
```json
{
  "success": true,
  "data_info": {
    "row_count": 120,
    "column_count": 2,
    "original_columns": ["Name", "Email"],
    "preview": [
      { "Combined_Text": "John <EMAIL>" },
      { "Combined_Text": "Jane <EMAIL>" }
    ]
  }
}
```

### ❌ Error Response:
```json
{
  "success": false,
  "error": "파일을 찾을 수 없습니다."
}
```

---

### 🧪 cURL Example:
```bash
curl -X POST http://localhost:5000/api/select-columns \
  -H "Content-Type: application/json" \
  -d '{"columns": ["Name", "Email"]}' \
  -c cookies.txt -b cookies.txt
```

### ⚛️ JavaScript (React / Fetch):
```js
fetch("http://localhost:5000/api/select-columns", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  credentials: "include",
  body: JSON.stringify({
    columns: ["Name", "Email"],
  }),
})
  .then(res => res.json())
  .then(data => console.log(data));
```