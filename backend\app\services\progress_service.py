# app/services/progress_service.py
import threading

# Global dict to store progress of tasks
# In a production app, use Redis or similar for this
task_progress = {}
progress_lock = threading.Lock()


def set_progress(value, task_id=None):
    """Set progress for a task"""
    global task_progress
    with progress_lock:
        # If no task_id provided, just update global progress
        if task_id is None:
            task_progress["global"] = value
        else:
            task_progress[task_id] = value


def get_task_progress(task_id=None):
    """Get progress for a task"""
    global task_progress
    with progress_lock:
        # If no task_id provided, return global progress
        if task_id is None or task_id == "global":
            return task_progress.get("global", 0)
        return task_progress.get(task_id, 0)
